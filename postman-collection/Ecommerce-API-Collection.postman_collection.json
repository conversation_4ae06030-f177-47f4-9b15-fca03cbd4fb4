{"info": {"_postman_id": "ecom-golang-api-collection", "name": "Ecommerce Golang Clean Architecture API", "description": "Complete API collection for Ecommerce platform built with Golang Clean Architecture\n\nThis collection includes:\n- Authentication & Authorization\n- User Management\n- Product Management\n- Category Management\n- Cart & Checkout\n- Order Management\n- Payment Processing\n- File Upload\n- Reviews & Ratings\n- Wishlist\n- Notifications\n- Analytics\n- Admin Operations\n- Search & Filtering\n- Recommendations\n- Shipping & Inventory\n\nBase URL: {{base_url}}/api/v1\nEnvironment Variables Required:\n- base_url: API base URL\n- jwt_token: Authentication token\n- admin_token: Admin authentication token\n- user_id: Current user ID\n- session_id: Session ID for guest operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "description": "User authentication and authorization endpoints", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user account. All fields except phone are required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has user data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('email');", "    pm.expect(responseJson.data).to.have.property('first_name');", "    pm.expect(responseJson.data).to.have.property('last_name');", "    pm.expect(responseJson.data).to.have.property('role');", "});", "", "pm.test('User role is customer by default', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.role).to.eql('customer');", "});"], "type": "text/javascript"}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"IPAddress\": \"127.0.0.1\",\n  \"UserAgent\": \"PostmanRuntime/7.32.3\",\n  \"DeviceInfo\": \"Postman API Client\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Authenticate user and receive JWT tokens. Returns access token and refresh token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains tokens', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('token');", "    pm.expect(responseJson.data).to.have.property('refresh_token');", "    pm.expect(responseJson.data).to.have.property('user');", "});", "", "pm.test('Store tokens in environment', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        pm.environment.set('jwt_token', responseJson.data.token);", "        pm.environment.set('refresh_token', responseJson.data.refresh_token);", "        pm.environment.set('user_id', responseJson.data.user.id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}, "description": "Refresh access token using refresh token. Returns new access token and refresh token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains new tokens', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('token');", "    pm.expect(responseJson.data).to.have.property('refresh_token');", "});", "", "pm.test('Update tokens in environment', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        pm.environment.set('jwt_token', responseJson.data.token);", "        pm.environment.set('refresh_token', responseJson.data.refresh_token);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout user and invalidate current JWT token. Requires valid JWT token in Authorization header."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Clear tokens from environment', function () {", "    pm.environment.unset('jwt_token');", "    pm.environment.unset('refresh_token');", "    pm.environment.unset('user_id');", "});"], "type": "text/javascript"}}]}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/forgot-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "forgot-password"]}, "description": "Send password reset email to user. Email must be registered in the system."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_from_email\",\n  \"password\": \"NewSecurePassword123!\",\n  \"password_confirmation\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/reset-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "reset-password"]}, "description": "Reset user password using token from forgot password email. Token expires after certain time."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Password reset successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('reset');", "});"], "type": "text/javascript"}}]}, {"name": "Verify Email (GET)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/verify-email?token=verification_token_from_email", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-email"], "query": [{"key": "token", "value": "verification_token_from_email", "description": "Email verification token from registration email"}, {"name": "Get Products by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/category/{{category_id}}?page=1&limit=12", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "category", "{{category_id}}"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "12", "description": "Items per page"}]}, "description": "Get products belonging to a specific category with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains products from category', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Trending Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/trending?page=1&limit=8", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "trending"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "8", "description": "Items per page"}]}, "description": "Get trending products based on recent sales and views."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains trending products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Related Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}/related?page=1&limit=6", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}", "related"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "6", "description": "Items per page"}]}, "description": "Get products related to a specific product based on category, tags, and attributes."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains related products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/filters?category_id={{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "filters"], "query": [{"key": "category_id", "value": "{{category_id}}", "description": "Filter by category", "disabled": true}]}, "description": "Get available filter options for products (brands, price ranges, attributes, etc.)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains filter options', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('brands');", "    pm.expect(responseJson.data).to.have.property('price_ranges');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/tree", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "tree"]}, "description": "Get complete category hierarchy as a tree structure."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category tree', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Root Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/root", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "root"]}, "description": "Get all root level categories (categories without parent)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains root categories', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Children", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}/children", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}", "children"]}, "description": "Get direct children of a specific category."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains child categories', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/slug/{{category_slug}}", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "slug", "{{category_slug}}"]}, "description": "Get category details by its slug (SEO-friendly URL)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('slug');", "});"], "type": "text/javascript"}}]}, {"name": "Update Brand", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Nike Updated\",\n  \"slug\": \"nike-updated\",\n  \"description\": \"Updated Nike brand description\",\n  \"logo\": \"https://example.com/nike-logo-new.png\",\n  \"website\": \"https://www.nike.com\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/brands/{{created_brand_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "brands", "{{created_brand_id}}"]}, "description": "Update an existing brand. All fields are required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Brand updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Brand", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/brands/{{created_brand_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "brands", "{{created_brand_id}}"]}, "description": "Delete a brand permanently. This will also update all products using this brand."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Brand deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Get Admin Analytics - Sales", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/sales?start_date=2024-01-01&end_date=2024-12-31&granularity=month", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "sales"], "query": [{"key": "start_date", "value": "2024-01-01", "description": "Start date for analytics"}, {"key": "end_date", "value": "2024-12-31", "description": "End date for analytics"}, {"key": "granularity", "value": "month", "description": "Data granularity: day, week, month"}]}, "description": "Get detailed sales analytics for admin dashboard."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains sales analytics', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('total_sales');", "    pm.expect(responseJson.data).to.have.property('order_count');", "    pm.expect(responseJson.data).to.have.property('average_order_value');", "});"], "type": "text/javascript"}}]}, {"name": "Get Admin Analytics - Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/products?limit=20&sort_by=sales&period=30d", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "products"], "query": [{"key": "limit", "value": "20", "description": "Number of products to analyze"}, {"key": "sort_by", "value": "sales", "description": "Sort by: sales, views, revenue"}, {"key": "period", "value": "30d", "description": "Analysis period"}]}, "description": "Get product performance analytics for admin."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains product analytics', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Admin Analytics - Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/users?period=30d&include_demographics=true", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "users"], "query": [{"key": "period", "value": "30d", "description": "Analysis period"}, {"key": "include_demographics", "value": "true", "description": "Include demographic data"}]}, "description": "Get user analytics and demographics for admin."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user analytics', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('total_users');", "    pm.expect(responseJson.data).to.have.property('new_users');", "});"], "type": "text/javascript"}}]}, {"name": "Track Admin Analytics Event", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event_name\": \"admin_action\",\n  \"event_category\": \"administration\",\n  \"properties\": {\n    \"action\": \"product_created\",\n    \"resource_id\": \"{{created_product_id}}\",\n    \"admin_id\": \"{{user_id}}\",\n    \"timestamp\": \"2024-01-01T12:00:00Z\"\n  },\n  \"metadata\": {\n    \"ip_address\": \"127.0.0.1\",\n    \"user_agent\": \"PostmanRuntime/7.32.3\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/analytics/events", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "events"]}, "description": "Track admin-specific analytics events."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Admin event tracked successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('tracked');", "});"], "type": "text/javascript"}}]}]}, "description": "Verify user email using token from registration email. Token is sent via query parameter."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email verification successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('verified');", "});"], "type": "text/javascript"}}]}, {"name": "Verify Email (POST)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"verification_token_from_email\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-email", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-email"]}, "description": "Verify user email using token from registration email. Token is sent in request body."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Email verification successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('verified');", "});"], "type": "text/javascript"}}]}, {"name": "Resend Verification Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-verification", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-verification"]}, "description": "Resend email verification link to user's email address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Verification email sent', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('sent');", "});"], "type": "text/javascript"}}]}, {"name": "Get Verification Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/verification/status", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "verification", "status"]}, "description": "Get current user's verification status including email verification, phone verification, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains verification status', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('email_verified');", "});"], "type": "text/javascript"}}]}, {"name": "Get Google OAuth URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/url", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "url"]}, "description": "Get Google OAuth authorization URL for frontend integration. Returns the OAuth URL and state parameter."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains OAuth URL and state', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('state');", "    pm.expect(responseJson.data.url).to.include('google');", "    ", "    // Store OAuth state for callback testing", "    pm.environment.set('oauth_state', responseJson.data.state);", "});"], "type": "text/javascript"}}]}, {"name": "Get Facebook OAuth URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/url", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "url"]}, "description": "Get Facebook OAuth authorization URL for frontend integration. Returns the OAuth URL and state parameter."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains OAuth URL and state', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('state');", "    pm.expect(responseJson.data.url).to.include('facebook');", "    ", "    // Store OAuth state for callback testing", "    pm.environment.set('facebook_oauth_state', responseJson.data.state);", "});"], "type": "text/javascript"}}]}, {"name": "Google OAuth Login (Direct Redirect)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "login"]}, "description": "Initiate Google OAuth login flow with direct redirect. This will redirect to Google's OAuth consent screen."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Response contains redirect location', function () {", "    pm.expect(pm.response.headers.get('Location')).to.include('google');", "});"], "type": "text/javascript"}}]}, {"name": "Facebook OAuth Login (Direct Redirect)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "login"]}, "description": "Initiate Facebook OAuth login flow with direct redirect. This will redirect to Facebook's OAuth consent screen."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Response contains redirect location', function () {", "    pm.expect(pm.response.headers.get('Location')).to.include('facebook');", "});"], "type": "text/javascript"}}]}, {"name": "Google OAuth <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/callback?code=sample_auth_code&state={{oauth_state}}", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "callback"], "query": [{"key": "code", "value": "sample_auth_code", "description": "Authorization code from Google OAuth"}, {"key": "state", "value": "{{oauth_state}}", "description": "OAuth state parameter for security"}]}, "description": "Handle Google OAuth callback with authorization code. This endpoint processes the OAuth response from Google and creates/logs in the user. Note: This will redirect to frontend with token in URL fragment."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect to frontend)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Response redirects to frontend callback', function () {", "    const location = pm.response.headers.get('Location');", "    pm.expect(location).to.include('localhost:3000/auth/google/callback');", "    ", "    // Extract token from redirect URL fragment if present", "    if (location && location.includes('#token=')) {", "        const tokenMatch = location.match(/#token=([^&]+)/);", "        if (tokenMatch && tokenMatch[1]) {", "            pm.environment.set('jwt_token', tokenMatch[1]);", "            console.log('OAuth token extracted and stored');", "        }", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Facebook OAuth <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/callback?code=sample_auth_code&state={{facebook_oauth_state}}", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "callback"], "query": [{"key": "code", "value": "sample_auth_code", "description": "Authorization code from Facebook OAuth"}, {"key": "state", "value": "{{facebook_oauth_state}}", "description": "OAuth state parameter for security"}]}, "description": "Handle Facebook OAuth callback with authorization code. This endpoint processes the OAuth response from Facebook and creates/logs in the user. Note: This will redirect to frontend with token in URL fragment."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect to frontend)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Response redirects to frontend callback', function () {", "    const location = pm.response.headers.get('Location');", "    pm.expect(location).to.include('localhost:3000/auth/facebook/callback');", "    ", "    // Extract token from redirect URL fragment if present", "    if (location && location.includes('#token=')) {", "        const tokenMatch = location.match(/#token=([^&]+)/);", "        if (tokenMatch && tokenMatch[1]) {", "            pm.environment.set('jwt_token', tokenMatch[1]);", "            console.log('OAuth token extracted and stored');", "        }", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "👤 User Management", "description": "User profile, preferences, and account management", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Get current user's profile information. Requires valid JWT token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user profile', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('email');", "    pm.expect(responseJson.data).to.have.property('first_name');", "    pm.expect(responseJson.data).to.have.property('last_name');", "    pm.expect(responseJson.data).to.have.property('role');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+*********1\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update current user's profile information. All fields are optional."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Profile updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"SecurePassword123!\",\n  \"new_password\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/change-password", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "change-password"]}, "description": "Change user's password. Requires current password for verification."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Password changed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('changed');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Preferences", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Get current user's preferences including theme, language, notifications, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains preferences', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theme\": \"dark\",\n  \"language\": \"en\",\n  \"currency\": \"USD\",\n  \"timezone\": \"America/New_York\",\n  \"email_notifications\": true,\n  \"sms_notifications\": false,\n  \"push_notifications\": true,\n  \"marketing_emails\": false,\n  \"order_updates\": true,\n  \"security_alerts\": true,\n  \"newsletter_enabled\": false,\n  \"promotional_emails\": false,\n  \"profile_visibility\": \"private\",\n  \"show_online_status\": false,\n  \"allow_data_collection\": true,\n  \"allow_personalization\": true,\n  \"allow_third_party_sharing\": false,\n  \"default_shipping_method\": \"standard\",\n  \"default_payment_method\": \"stripe\",\n  \"save_payment_methods\": true,\n  \"auto_apply_coupons\": true,\n  \"wishlist_visibility\": \"private\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Update user preferences. All fields are optional."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Preferences updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Update Theme Preference", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theme\": \"dark\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences/theme", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences", "theme"]}, "description": "Update user's theme preference (light, dark, auto)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Theme updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Update Language Preference", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences/language", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences", "language"]}, "description": "Update user's language preference (en, vi, fr, es, etc.)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Language updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Sessions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/sessions?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "sessions"], "query": [{"key": "limit", "value": "10", "description": "Number of sessions to return"}, {"key": "offset", "value": "0", "description": "Number of sessions to skip"}]}, "description": "Get current user's active sessions with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains sessions', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    ", "    // Store first session ID for invalidation test", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('first_session_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Invalidate Specific Session", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/sessions/{{first_session_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "sessions", "{{first_session_id}}"]}, "description": "Invalidate a specific user session by session ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Session invalidated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('invalidated');", "});"], "type": "text/javascript"}}]}, {"name": "Invalidate All Sessions", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/sessions", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "sessions"]}, "description": "Invalidate all user sessions except the current one."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('All sessions invalidated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('invalidated');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Preferences", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Get user preferences and settings."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User preferences returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('preferences');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theme\": \"dark\",\n  \"language\": \"en\",\n  \"currency\": \"USD\",\n  \"timezone\": \"America/New_York\",\n  \"notifications\": {\n    \"email\": true,\n    \"push\": false,\n    \"sms\": false\n  },\n  \"privacy\": {\n    \"profile_visibility\": \"public\",\n    \"activity_tracking\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Update user preferences and settings."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Preferences updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Update Theme", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theme\": \"light\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences/theme", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences", "theme"]}, "description": "Update user theme preference."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Theme updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Update Language", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"language\": \"vi\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences/language", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences", "language"]}, "description": "Update user language preference."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Language updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 OAuth Authentication", "description": "OAuth authentication endpoints for Google and Facebook", "item": [{"name": "Get Google OAuth URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/url", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "url"]}, "description": "Get Google OAuth authorization URL for frontend integration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Google OAuth URL retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('state');", "    pm.expect(responseJson.data.url).to.include('accounts.google.com');", "    ", "    // Store OAuth state for callback", "    pm.environment.set('oauth_state', responseJson.data.state);", "});"], "type": "text/javascript"}}]}, {"name": "Get Facebook OAuth URL", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/url", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "url"]}, "description": "Get Facebook OAuth authorization URL for frontend integration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Facebook OAuth URL retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('state');", "    pm.expect(responseJson.data.url).to.include('facebook.com');", "    ", "    // Store OAuth state for callback", "    pm.environment.set('facebook_oauth_state', responseJson.data.state);", "});"], "type": "text/javascript"}}]}, {"name": "Google OAuth <PERSON>gin (Redirect)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/google/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "google", "login"]}, "description": "Initiate Google OAuth login flow (redirects to Google). This endpoint will redirect to Google OAuth consent screen."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Redirect location contains Google OAuth URL', function () {", "    const location = pm.response.headers.get('Location');", "    pm.expect(location).to.include('accounts.google.com');", "});"], "type": "text/javascript"}}]}, {"name": "Facebook OAuth Login (Redirect)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/auth/facebook/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "facebook", "login"]}, "description": "Initiate Facebook OAuth login flow (redirects to Facebook). This endpoint will redirect to Facebook OAuth consent screen."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 302 (redirect)', function () {", "    pm.response.to.have.status(302);", "});", "", "pm.test('Redirect location contains Facebook OAuth URL', function () {", "    const location = pm.response.headers.get('Location');", "    pm.expect(location).to.include('facebook.com');", "});"], "type": "text/javascript"}}]}]}, {"name": "🛍️ Products", "description": "Product catalog, search, filtering, and management", "item": [{"name": "📋 Public Product Operations", "description": "Public product endpoints accessible without authentication", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products?page=1&limit=12&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "products"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "12", "description": "Items per page (default: 12, max: 100)"}, {"key": "sort_by", "value": "created_at", "description": "Sort field: name, price, created_at, updated_at"}, {"key": "sort_order", "value": "desc", "description": "Sort order: asc, desc"}]}, "description": "Get paginated list of all active products with basic filtering and sorting."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains products array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "    pm.expect(responseJson.pagination).to.have.property('page');", "    pm.expect(responseJson.pagination).to.have.property('limit');", "    pm.expect(responseJson.pagination).to.have.property('total');", "});", "", "pm.test('Store first product ID', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('first_product_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{first_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{first_product_id}}"]}, "description": "Get detailed information about a specific product by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains product details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('price');", "    pm.expect(responseJson.data).to.have.property('description');", "    pm.expect(responseJson.data).to.have.property('sku');", "});"], "type": "text/javascript"}}]}, {"name": "Search Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/search?q=laptop&page=1&limit=12&min_price=100&max_price=2000&category_id=&brand_id=&rating=4&sort_by=price&sort_order=asc", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "search"], "query": [{"key": "q", "value": "laptop", "description": "Search query (product name, description, tags)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "12", "description": "Items per page"}, {"key": "min_price", "value": "100", "description": "Minimum price filter"}, {"key": "max_price", "value": "2000", "description": "Maximum price filter"}, {"key": "category_id", "value": "", "description": "Filter by category ID"}, {"key": "brand_id", "value": "", "description": "Filter by brand ID"}, {"key": "rating", "value": "4", "description": "Minimum rating filter"}, {"key": "sort_by", "value": "price", "description": "Sort by: name, price, rating, created_at"}, {"key": "sort_order", "value": "asc", "description": "Sort order: asc, desc"}]}, "description": "Search products with advanced filtering options including price range, category, brand, rating, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains search results', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Search results match query', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        const searchQuery = pm.request.url.query.get('q');", "        if (searchQuery) {", "            const firstProduct = responseJson.data[0];", "            const productText = (firstProduct.name + ' ' + firstProduct.description).toLowerCase();", "            pm.expect(productText).to.include(searchQuery.toLowerCase());", "        }", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Featured Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/featured?page=1&limit=8", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "featured"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "8", "description": "Items per page"}]}, "description": "Get featured products that are marked as featured by admin."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains featured products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('All products are featured', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        responseJson.data.forEach(product => {", "            pm.expect(product.featured).to.be.true;", "        });", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Search Suggestions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/suggestions?q=lap&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "suggestions"], "query": [{"key": "q", "value": "lap", "description": "Search query for suggestions"}, {"key": "limit", "value": "10", "description": "Maximum number of suggestions (default: 10)"}]}, "description": "Get search suggestions based on partial query input."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains suggestions', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Popular Searches", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/popular-searches?limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "popular-searches"], "query": [{"key": "limit", "value": "20", "description": "Maximum number of popular searches (default: 20)"}]}, "description": "Get list of popular search terms."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains popular searches', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔍 Advanced Product Search & Filters", "description": "Advanced search, filtering, and user-specific features (requires authentication for some endpoints)", "item": [{"name": "Get Search History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products/search-history?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "search-history"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}]}, "description": "Get user's search history. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains search history', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Filter Sets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products/filter-sets/user?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "filter-sets", "user"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}]}, "description": "Get user's saved filter sets. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains filter sets', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Create Filter Set", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Gaming Laptops Under $2000\",\n  \"description\": \"High-performance gaming laptops within budget\",\n  \"filters\": {\n    \"category_id\": \"{{laptop_category_id}}\",\n    \"min_price\": 800,\n    \"max_price\": 2000,\n    \"attributes\": {\n      \"ram\": [\"16GB\", \"32GB\"],\n      \"gpu\": [\"RTX 3060\", \"RTX 3070\", \"RTX 3080\"]\n    },\n    \"brands\": [\"{{gaming_brand_id}}\"]\n  },\n  \"is_public\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/products/filter-sets", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "filter-sets"]}, "description": "Create a new filter set. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Filter set created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    ", "    // Store filter set ID for other tests", "    pm.environment.set('filter_set_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Update Filter Set", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Gaming Laptops Under $2500\",\n  \"description\": \"Updated high-performance gaming laptops within budget\",\n  \"filters\": {\n    \"category_id\": \"{{laptop_category_id}}\",\n    \"min_price\": 1000,\n    \"max_price\": 2500,\n    \"attributes\": {\n      \"ram\": [\"16GB\", \"32GB\", \"64GB\"],\n      \"gpu\": [\"RTX 3070\", \"RTX 3080\", \"RTX 4060\"]\n    }\n  },\n  \"is_public\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/products/filter-sets/{{filter_set_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "filter-sets", "{{filter_set_id}}"]}, "description": "Update an existing filter set. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filter set updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Filter Set", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products/filter-sets/{{filter_set_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "filter-sets", "{{filter_set_id}}"]}, "description": "Delete a filter set. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filter set deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 Admin Product Management", "description": "Admin-only product management endpoints (requires admin authentication)", "item": [{"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Laptop\",\n  \"description\": \"High-performance laptop with latest specifications and cutting-edge technology for professionals\",\n  \"short_description\": \"Premium laptop for professionals\",\n  \"sku\": \"LAPTOP-001\",\n  \"slug\": \"premium-laptop\",\n  \"meta_title\": \"Premium Laptop - High Performance\",\n  \"meta_description\": \"Buy premium laptop with latest specs\",\n  \"keywords\": \"laptop, premium, high-performance\",\n  \"featured\": true,\n  \"visibility\": \"visible\",\n  \"price\": 1299.99,\n  \"compare_price\": 1499.99,\n  \"cost_price\": 999.99,\n  \"sale_price\": 1199.99,\n  \"sale_start_date\": \"2024-01-01T00:00:00Z\",\n  \"sale_end_date\": \"2024-12-31T23:59:59Z\",\n  \"stock\": 50,\n  \"low_stock_threshold\": 5,\n  \"track_quantity\": true,\n  \"allow_backorder\": false,\n  \"weight\": 2.5,\n  \"dimensions\": {\n    \"length\": 35.0,\n    \"width\": 25.0,\n    \"height\": 2.0\n  },\n  \"requires_shipping\": true,\n  \"shipping_class\": \"standard\",\n  \"tax_class\": \"standard\",\n  \"country_of_origin\": \"US\",\n  \"category_id\": \"{{category_id}}\",\n  \"brand_id\": \"{{brand_id}}\",\n  \"images\": [\n    {\n      \"url\": \"https://example.com/laptop1.jpg\",\n      \"alt_text\": \"Premium Laptop Front View\",\n      \"position\": 1\n    },\n    {\n      \"url\": \"https://example.com/laptop2.jpg\",\n      \"alt_text\": \"Premium Laptop Side View\",\n      \"position\": 2\n    }\n  ],\n  \"tags\": [\"laptop\", \"premium\", \"electronics\"],\n  \"attributes\": [\n    {\n      \"attribute_id\": \"{{color_attribute_id}}\",\n      \"term_id\": null,\n      \"value\": \"Silver\",\n      \"position\": 1\n    },\n    {\n      \"attribute_id\": \"{{ram_attribute_id}}\",\n      \"term_id\": null,\n      \"value\": \"16GB\",\n      \"position\": 2\n    },\n    {\n      \"attribute_id\": \"{{storage_attribute_id}}\",\n      \"term_id\": null,\n      \"value\": \"512GB SSD\",\n      \"position\": 3\n    }\n  ],\n  \"variants\": [],\n  \"status\": \"active\",\n  \"product_type\": \"simple\",\n  \"is_digital\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products"]}, "description": "Create a new product. Requires admin authentication. All product fields can be specified."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Product created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('sku');", "    ", "    // Store created product ID for other tests", "    pm.environment.set('created_product_id', responseJson.data.id);", "});", "", "pm.test('Product has correct data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "    pm.expect(responseJson.data.sku).to.equal(requestBody.sku);", "    pm.expect(responseJson.data.price).to.equal(requestBody.price);", "});"], "type": "text/javascript"}}]}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Premium Laptop\",\n  \"description\": \"Updated high-performance laptop with latest specifications\",\n  \"price\": 1399.99,\n  \"compare_price\": 1599.99,\n  \"stock\": 45,\n  \"featured\": true,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}"]}, "description": "Update an existing product. All fields are optional. Only provided fields will be updated."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});", "", "pm.test('Product has updated data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "    pm.expect(responseJson.data.price).to.equal(requestBody.price);", "});"], "type": "text/javascript"}}]}, {"name": "Patch Product", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 1299.99,\n  \"stock\": 30,\n  \"featured\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}"]}, "description": "Partially update a product. Only provided fields will be updated, others remain unchanged."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product patched successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});"], "type": "text/javascript"}}]}, {"name": "Update Inventory", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"stock\": 25\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}/stock", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}", "stock"]}, "description": "Update product stock quantity. This will also automatically update stock status."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Stock updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/products/{{created_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{created_product_id}}"]}, "description": "Delete a product permanently. This will also remove the product from all carts."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔧 Advanced Product Filtering", "description": "Advanced product filtering and attribute management", "item": [{"name": "Filter Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/filter?category_id={{category_id}}&min_price=100&max_price=1000&brand_id={{brand_id}}&attributes[color]=red&attributes[size]=large&sort_by=price&sort_order=asc&page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "filter"], "query": [{"key": "category_id", "value": "{{category_id}}", "description": "Filter by category ID"}, {"key": "min_price", "value": "100", "description": "Minimum price filter"}, {"key": "max_price", "value": "1000", "description": "Maximum price filter"}, {"key": "brand_id", "value": "{{brand_id}}", "description": "Filter by brand ID"}, {"key": "attributes[color]", "value": "red", "description": "Filter by color attribute"}, {"key": "attributes[size]", "value": "large", "description": "Filter by size attribute"}, {"key": "sort_by", "value": "price", "description": "Sort field"}, {"key": "sort_order", "value": "asc", "description": "Sort order"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Results per page"}]}, "description": "Advanced product filtering with multiple criteria."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filtered products returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data).to.have.property('total');", "    pm.expect(responseJson.data).to.have.property('filters_applied');", "});"], "type": "text/javascript"}}]}, {"name": "Get Filter Facets", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/facets?category_id={{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "facets"], "query": [{"key": "category_id", "value": "{{category_id}}", "description": "Category ID to get facets for"}]}, "description": "Get available filter facets for products in a category."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filter facets returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('brands');", "    pm.expect(responseJson.data).to.have.property('price_ranges');", "    pm.expect(responseJson.data).to.have.property('attributes');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Attributes", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/attributes", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "attributes"]}, "description": "Get all available product attributes for filtering."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product attributes returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    ", "    // Store first attribute ID", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('color_attribute_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Attribute Terms", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/attributes/{{color_attribute_id}}/terms", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "attributes", "{{color_attribute_id}}", "terms"]}, "description": "Get available terms for a specific product attribute."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Attribute terms returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "📂 Categories", "description": "Product category management and hierarchy", "item": [{"name": "📋 Public Category Operations", "description": "Public category endpoints accessible without authentication", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20, max: 100)"}]}, "description": "Get paginated list of all active categories."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains categories array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "});", "", "pm.test('Store first category ID', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('category_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}"]}, "description": "Get detailed information about a specific category by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('slug');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/tree", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "tree"]}, "description": "Get hierarchical category tree structure with parent-child relationships."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category tree', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔍 Advanced Category Features", "description": "Advanced category search, trending, and SEO features", "item": [{"name": "Search Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/search?q=electronics&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "search"], "query": [{"key": "q", "value": "electronics", "description": "Search query for category names and descriptions"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}]}, "description": "Search categories by name and description."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains search results', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Trending Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/trending?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "trending"], "query": [{"key": "limit", "value": "10", "description": "Maximum number of trending categories"}]}, "description": "Get trending categories based on recent activity."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains trending categories', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Popular Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/popular?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "popular"], "query": [{"key": "limit", "value": "10", "description": "Maximum number of popular categories"}]}, "description": "Get popular categories based on product count and sales."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains popular categories', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Path", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}/path", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}", "path"]}, "description": "Get the full path from root to the specified category."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category path', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Product Count", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}/count", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}", "count"]}, "description": "Get the total number of products in the specified category."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains product count', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category Landing Page", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}/landing", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}", "landing"]}, "description": "Get category landing page data including featured products and banners."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains landing page data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Get Category SEO", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}/seo", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}", "seo"]}, "description": "Get category SEO information including meta tags and structured data."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains SEO data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 Admin Category Management", "description": "Admin-only category management endpoints (requires admin authentication)", "item": [{"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Electronics\",\n  \"description\": \"Electronic devices and gadgets\",\n  \"slug\": \"electronics\",\n  \"image\": \"https://example.com/electronics.jpg\",\n  \"parent_id\": null,\n  \"is_active\": true,\n  \"sort_order\": 1,\n  \"seo\": {\n    \"meta_title\": \"Electronics - Latest Gadgets and Devices\",\n    \"meta_description\": \"Shop the latest electronics, gadgets, and devices at great prices\",\n    \"keywords\": \"electronics, gadgets, devices, technology\",\n    \"canonical_url\": \"https://example.com/categories/electronics\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "categories"]}, "description": "Create a new category. Requires admin authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Category created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('slug');", "    ", "    // Store created category ID for other tests", "    pm.environment.set('created_category_id', responseJson.data.id);", "});", "", "pm.test('Category has correct data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "    pm.expect(responseJson.data.slug).to.equal(requestBody.slug);", "});"], "type": "text/javascript"}}]}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Electronics\",\n  \"description\": \"Updated electronic devices and gadgets\",\n  \"is_active\": true,\n  \"sort_order\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/categories/{{created_category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "categories", "{{created_category_id}}"]}, "description": "Update an existing category. All fields are optional."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Category updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});", "", "pm.test('Category has updated data', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.name).to.equal(requestBody.name);", "});"], "type": "text/javascript"}}]}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/categories/{{created_category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "categories", "{{created_category_id}}"]}, "description": "Delete a category permanently. This will also update all products using this category."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Category deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Get Categories with Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/filter?has_products=true&min_product_count=5&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "filter"], "query": [{"key": "has_products", "value": "true", "description": "Filter categories that have products"}, {"key": "min_product_count", "value": "5", "description": "Minimum product count"}, {"key": "is_active", "value": "true", "description": "Filter active categories only"}]}, "description": "Get categories with advanced filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filtered categories returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Validate Category Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/slug/validate?slug=new-category-slug", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "slug", "validate"], "query": [{"key": "slug", "value": "new-category-slug", "description": "Slug to validate"}]}, "description": "Validate if a category slug is available."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Slug validation result returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('available');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🚚 Shipping", "description": "Shipping methods, rates calculation, and tracking", "item": [{"name": "📋 Public Shipping Operations", "description": "Public shipping endpoints accessible without authentication", "item": [{"name": "Get Shipping Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/methods", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "methods"]}, "description": "Get all available shipping methods."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping methods', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Calculate Distance-Based Shipping", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"origin\": {\n    \"address\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"US\"\n  },\n  \"destination\": {\n    \"address\": \"456 Oak Ave\",\n    \"city\": \"Los Angeles\",\n    \"state\": \"CA\",\n    \"zip_code\": \"90210\",\n    \"country\": \"US\"\n  },\n  \"weight\": 2.5,\n  \"dimensions\": {\n    \"length\": 10,\n    \"width\": 8,\n    \"height\": 6\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/shipping/calculate-distance", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "calculate-distance"]}, "description": "Calculate shipping cost based on distance between origin and destination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping calculation', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('cost');", "});"], "type": "text/javascript"}}]}, {"name": "Get Shipping Zones", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/zones", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "zones"]}, "description": "Get all shipping zones and their configurations."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping zones', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Calculate Shipping Rates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"destination\": {\n    \"address\": \"456 Oak Ave\",\n    \"city\": \"Los Angeles\",\n    \"state\": \"CA\",\n    \"zip_code\": \"90210\",\n    \"country\": \"US\"\n  },\n  \"items\": [\n    {\n      \"product_id\": \"{{product_id}}\",\n      \"quantity\": 2,\n      \"weight\": 1.5,\n      \"dimensions\": {\n        \"length\": 8,\n        \"width\": 6,\n        \"height\": 4\n      }\n    }\n  ],\n  \"shipping_method_id\": \"standard\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/shipping/rates", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "rates"]}, "description": "Calculate shipping rates for given items and destination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping rates', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('rates');", "});"], "type": "text/javascript"}}]}, {"name": "Validate Shipping Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"address\": \"456 Oak Avenue\",\n  \"city\": \"Los Angeles\",\n  \"state\": \"CA\",\n  \"zip_code\": \"90210\",\n  \"country\": \"US\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/shipping/validate-address", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "validate-address"]}, "description": "Validate and standardize shipping address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains address validation', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('is_valid');", "});"], "type": "text/javascript"}}]}, {"name": "Track Shipment", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/track/{{tracking_number}}", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "track", "{{tracking_number}}"]}, "description": "Track shipment by tracking number."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains tracking information', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('status');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 Admin Shipping Management", "description": "Admin-only shipping management endpoints (requires admin authentication)", "item": [{"name": "Create Shipment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"shipping_method_id\": \"{{shipping_method_id}}\",\n  \"carrier\": \"FedEx\",\n  \"tracking_number\": \"**********\",\n  \"weight\": 2.5,\n  \"dimensions\": \"30x20x10 cm\",\n  \"package_count\": 1,\n  \"insurance_value\": 100.00,\n  \"estimated_delivery\": \"2024-02-15T10:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/shipments", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "shipments"]}, "description": "Create a new shipment for an order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Shipment created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('tracking_number');", "    ", "    // Store shipment ID for other tests", "    pm.environment.set('shipment_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Get Shipment", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/shipments/{{shipment_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "shipments", "{{shipment_id}}"]}, "description": "Get shipment details by ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipment details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('tracking_number');", "});"], "type": "text/javascript"}}]}, {"name": "Update Shipment Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_transit\",\n  \"location\": \"Los Angeles Distribution Center\",\n  \"notes\": \"Package is on its way to destination\",\n  \"estimated_delivery\": \"2024-02-16T14:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/shipments/{{shipment_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "shipments", "{{shipment_id}}", "status"]}, "description": "Update shipment status and tracking information."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Shipment status updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('status');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🏷️ Brands", "description": "Brand management and information", "item": [{"name": "📋 Public Brand Operations", "description": "Public brand endpoints accessible without authentication", "item": [{"name": "Get All Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands?page=1&limit=20&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "brands"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20, max: 100)"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}]}, "description": "Get paginated list of all brands with optional filtering."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains brands array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Brand by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/{{brand_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "{{brand_id}}"]}, "description": "Get detailed information about a specific brand by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains brand data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "});"], "type": "text/javascript"}}]}, {"name": "Get Brand by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/slug/{{brand_slug}}", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "slug", "{{brand_slug}}"]}, "description": "Get brand details by its slug (SEO-friendly URL)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains brand data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('slug');", "});"], "type": "text/javascript"}}]}, {"name": "Get Active Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/active", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "active"]}, "description": "Get all active brands without pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains active brands', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Popular Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/popular?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "popular"], "query": [{"key": "limit", "value": "10", "description": "Number of popular brands to return"}]}, "description": "Get popular brands based on product count and sales."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains popular brands', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Search Brands", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/brands/search?q=nike&page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "brands", "search"], "query": [{"key": "q", "value": "nike", "description": "Search query (brand name)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}]}, "description": "Search brands by name with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains search results', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔐 Admin Brand Management", "description": "Admin-only brand management endpoints (requires admin authentication)", "item": [{"name": "Create Brand", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"slug\": \"nike\",\n  \"description\": \"Just Do It - Leading sports brand\",\n  \"logo\": \"https://example.com/nike-logo.png\",\n  \"website\": \"https://www.nike.com\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/brands", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "brands"]}, "description": "Create a new brand. Requires admin authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Brand created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.environment.set('created_brand_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🛒 Shopping Cart", "description": "Shopping cart operations for authenticated and guest users", "item": [{"name": "📋 Cart Operations", "description": "Shopping cart management endpoints", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "X-Session-ID", "value": "{{session_id}}", "description": "Required for guest cart operations"}], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}, "description": "Get current user's cart or guest cart by session ID. For authenticated users, Authorization header is required. For guest users, X-Session-ID header is required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains cart data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data.items).to.be.an('array');", "    pm.expect(responseJson.data).to.have.property('subtotal');", "    pm.expect(responseJson.data).to.have.property('total');", "});", "", "pm.test('Store cart ID', function () {", "    const responseJson = pm.response.json();", "    pm.environment.set('cart_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "X-Session-ID", "value": "{{session_id}}", "description": "Required for guest cart operations"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{first_product_id}}\",\n  \"quantity\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items"]}, "description": "Add a product to the shopping cart. Works for both authenticated users and guest users."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Item added to cart successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data.items.length).to.be.greaterThan(0);", "});", "", "pm.test('Cart totals updated', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.subtotal).to.be.greaterThan(0);", "    pm.expect(responseJson.data.total).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}]}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 3\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items/{{first_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items", "{{first_product_id}}"]}, "description": "Update the quantity of an item in the cart. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Cart item updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "});", "", "pm.test('Item quantity updated', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    const updatedItem = responseJson.data.items.find(item => item.product.id === pm.environment.get('first_product_id'));", "    if (updatedItem) {", "        pm.expect(updatedItem.quantity).to.equal(requestBody.quantity);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Remove from Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/cart/items/{{first_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items", "{{first_product_id}}"]}, "description": "Remove a specific product from the cart. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Item removed from cart successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "});", "", "pm.test('Item no longer in cart', function () {", "    const responseJson = pm.response.json();", "    const removedItem = responseJson.data.items.find(item => item.product.id === pm.environment.get('first_product_id'));", "    pm.expect(removedItem).to.be.undefined;", "});"], "type": "text/javascript"}}]}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}, "description": "Remove all items from the cart. Requires authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON><PERSON> cleared successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('cleared');", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"strategy\": \"auto\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/merge", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "merge"]}, "description": "Merge guest cart with user cart when user logs in. Strategy options: auto, replace, keep_user, merge."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON><PERSON> merged successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "});"], "type": "text/javascript"}}]}]}, {"name": "🛍️ Guest Cart Operations", "description": "Guest cart operations using session ID (no authentication required)", "item": [{"name": "Get Guest Cart", "request": {"method": "GET", "header": [{"key": "X-Session-ID", "value": "{{guest_session_id}}", "description": "Session ID for guest cart (16-128 characters)"}], "url": {"raw": "{{base_url}}/api/v1/guest/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "guest", "cart"]}, "description": "Get guest cart by session ID. Creates new cart if not exists."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Guest cart retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data).to.have.property('session_id');", "    pm.expect(responseJson.data.session_id).to.eql(pm.environment.get('guest_session_id'));", "});"], "type": "text/javascript"}}]}, {"name": "Add to <PERSON>", "request": {"method": "POST", "header": [{"key": "X-Session-ID", "value": "{{guest_session_id}}", "description": "Session ID for guest cart"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{first_product_id}}\",\n  \"quantity\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/guest/cart/items", "host": ["{{base_url}}"], "path": ["api", "v1", "guest", "cart", "items"]}, "description": "Add product to guest cart using session ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Item added to guest cart successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data.items.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}]}, {"name": "Update Guest <PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "X-Session-ID", "value": "{{guest_session_id}}", "description": "Session ID for guest cart"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 3\n}"}, "url": {"raw": "{{base_url}}/api/v1/guest/cart/items/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "guest", "cart", "items", "{{product_id}}"]}, "description": "Update quantity or attributes of item in guest cart."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Guest cart item updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Remove from <PERSON> Cart", "request": {"method": "DELETE", "header": [{"key": "X-Session-ID", "value": "{{guest_session_id}}", "description": "Session ID for guest cart"}], "url": {"raw": "{{base_url}}/api/v1/guest/cart/items/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "guest", "cart", "items", "{{product_id}}"]}, "description": "Remove specific item from guest cart."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Item removed from guest cart successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON> Guest Cart", "request": {"method": "DELETE", "header": [{"key": "X-Session-ID", "value": "{{guest_session_id}}", "description": "Session ID for guest cart"}], "url": {"raw": "{{base_url}}/api/v1/guest/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "guest", "cart"]}, "description": "Clear all items from guest cart."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Guest cart cleared successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "💳 Checkout & Orders", "description": "Checkout process and order management", "item": [{"name": "🛒 Checkout Session", "description": "Checkout session management before order creation", "item": [{"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shipping_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"company\": \"Acme Corp\",\n    \"address1\": \"123 Main St\",\n    \"address2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"US\",\n    \"phone\": \"+**********\"\n  },\n  \"billing_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"company\": \"Acme Corp\",\n    \"address1\": \"123 Main St\",\n    \"address2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"US\",\n    \"phone\": \"+**********\"\n  },\n  \"payment_method\": \"stripe\",\n  \"notes\": \"Please handle with care\",\n  \"tax_rate\": 0.08,\n  \"shipping_cost\": 9.99,\n  \"discount_amount\": 5.00\n}"}, "url": {"raw": "{{base_url}}/api/v1/checkout/session", "host": ["{{base_url}}"], "path": ["api", "v1", "checkout", "session"]}, "description": "Create a checkout session with shipping/billing addresses and payment method before creating the order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Checkout session created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('session_id');", "    pm.expect(responseJson.data).to.have.property('total');", "    ", "    // Store checkout session info", "    pm.environment.set('checkout_session_id', responseJson.data.id);", "    pm.environment.set('checkout_session_token', responseJson.data.session_id);", "});"], "type": "text/javascript"}}]}, {"name": "Get Checkout Session", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/checkout/session/{{checkout_session_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "checkout", "session", "{{checkout_session_id}}"]}, "description": "Get checkout session details by ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Checkout session details returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('status');", "});"], "type": "text/javascript"}}]}]}, {"name": "📋 Customer Order Operations", "description": "Customer order endpoints (requires authentication)", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"shipping_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"address1\": \"123 Main St\",\n    \"address2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"USA\",\n    \"phone\": \"+**********\"\n  },\n  \"billing_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"address1\": \"123 Main St\",\n    \"address2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"USA\",\n    \"phone\": \"+**********\"\n  },\n  \"payment_method\": \"bank_transfer\",\n  \"notes\": \"Please deliver after 5 PM\",\n  \"tax_rate\": 0.08,\n  \"shipping_cost\": 10.00,\n  \"discount_amount\": 5.00\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"]}, "description": "Create a new order from user's cart. Currently supports bank transfer payment method only."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Order created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('order_number');", "    pm.expect(responseJson.data).to.have.property('status');", "    ", "    // Store created order ID for other tests", "    pm.environment.set('order_id', responseJson.data.id);", "    pm.environment.set('order_number', responseJson.data.order_number);", "});", "", "pm.test('Order has correct payment method', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.payment_method).to.equal('bank_transfer');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders?page=1&limit=10&status=&payment_status=&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Items per page"}, {"key": "status", "value": "", "description": "Filter by order status: pending, confirmed, processing, shipped, delivered, cancelled"}, {"key": "payment_status", "value": "", "description": "Filter by payment status: pending, paid, failed, refunded"}, {"key": "sort_by", "value": "created_at", "description": "Sort by field"}, {"key": "sort_order", "value": "desc", "description": "Sort order: asc, desc"}]}, "description": "Get current user's order history with optional filtering and sorting."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains orders array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});", "", "pm.test('Response contains pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}"]}, "description": "Get detailed information about a specific order by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains order details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('order_number');", "    pm.expect(responseJson.data).to.have.property('status');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data.items).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Changed my mind\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/cancel", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "cancel"]}, "description": "Cancel an existing order. Only orders with certain statuses can be cancelled."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Order cancelled successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data.status).to.equal('cancelled');", "});"], "type": "text/javascript"}}]}, {"name": "Get Order Events", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/events?public=true", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "events"], "query": [{"key": "public", "value": "true", "description": "Show only public events (default: false)"}]}, "description": "Get order events/timeline showing order status changes and activities."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains events array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "💰 Payments", "description": "Payment processing, refunds, and payment methods", "item": [{"name": "📋 Payment Operations", "description": "Payment processing and management endpoints", "item": [{"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"amount\": 99.99,\n  \"currency\": \"USD\",\n  \"description\": \"Order payment\",\n  \"success_url\": \"https://example.com/success\",\n  \"cancel_url\": \"https://example.com/cancel\",\n  \"metadata\": {\n    \"customer_id\": \"{{user_id}}\",\n    \"order_number\": \"{{order_number}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments/checkout-session", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "checkout-session"]}, "description": "Create a Stripe checkout session for payment processing."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Checkout session created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('session_id');", "    pm.expect(responseJson.data).to.have.property('session_url');", "    ", "    // Store session info for other tests", "    pm.environment.set('checkout_session_id', responseJson.data.session_id);", "    pm.environment.set('checkout_session_url', responseJson.data.session_url);", "});", "", "pm.test('Session URL is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.session_url).to.include('https://');", "});"], "type": "text/javascript"}}]}, {"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"amount\": 99.99,\n  \"currency\": \"USD\",\n  \"method\": \"credit_card\",\n  \"payment_token\": \"tok_visa\",\n  \"payment_method_id\": \"{{payment_method_id}}\",\n  \"billing_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"address1\": \"123 Main St\",\n    \"address2\": \"Apt 4B\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"US\"\n  },\n  \"metadata\": {\n    \"customer_id\": \"{{user_id}}\",\n    \"order_number\": \"{{order_number}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments", "host": ["{{base_url}}"], "path": ["api", "v1", "payments"]}, "description": "Process a direct payment for an order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Payment processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('status');", "    pm.expect(responseJson.data).to.have.property('transaction_id');", "    ", "    // Store payment ID for other tests", "    pm.environment.set('payment_id', responseJson.data.id);", "});", "", "pm.test('Payment has correct amount', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.amount).to.equal(requestBody.amount);", "});"], "type": "text/javascript"}}]}, {"name": "Get Payment by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/payments/{{payment_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "{{payment_id}}"]}, "description": "Get detailed information about a specific payment by its ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains payment details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('order_id');", "    pm.expect(responseJson.data).to.have.property('amount');", "    pm.expect(responseJson.data).to.have.property('status');", "    pm.expect(responseJson.data).to.have.property('method');", "});"], "type": "text/javascript"}}]}, {"name": "Process Refund", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"payment_id\": \"{{payment_id}}\",\n  \"order_id\": \"{{order_id}}\",\n  \"amount\": 50.00,\n  \"reason\": \"customer_request\",\n  \"description\": \"Customer requested refund for damaged item\",\n  \"type\": \"partial\",\n  \"force_approval\": false,\n  \"processed_by\": \"{{user_id}}\",\n  \"metadata\": {\n    \"refund_method\": \"original_payment\",\n    \"processing_fee\": 2.50,\n    \"admin_notes\": \"Approved by customer service\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/payments/{{payment_id}}/refund", "host": ["{{base_url}}"], "path": ["api", "v1", "payments", "{{payment_id}}", "refund"]}, "description": "Process a refund for a payment. Can be partial or full refund."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Refund processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('status');", "    ", "    // Store refund ID for other tests", "    pm.environment.set('refund_id', responseJson.data.id);", "});", "", "pm.test('Refund has correct amount', function () {", "    const responseJson = pm.response.json();", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.expect(responseJson.data.amount).to.equal(requestBody.amount);", "});"], "type": "text/javascript"}}]}, {"name": "Get Order Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/payments", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "payments"]}, "description": "Get all payments associated with a specific order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains payments array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔗 Webhooks", "description": "Payment webhook endpoints for external payment providers", "item": [{"name": "Payment Webhook (Stripe)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Stripe-Signature", "value": "t=**********,v1=sample_signature", "description": "Stripe webhook signature for verification"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"evt_**********\",\n  \"object\": \"event\",\n  \"api_version\": \"2020-08-27\",\n  \"created\": **********,\n  \"data\": {\n    \"object\": {\n      \"id\": \"pi_**********\",\n      \"object\": \"payment_intent\",\n      \"amount\": 2000,\n      \"currency\": \"usd\",\n      \"status\": \"succeeded\"\n    }\n  },\n  \"livemode\": false,\n  \"pending_webhooks\": 1,\n  \"request\": {\n    \"id\": \"req_**********\",\n    \"idempotency_key\": null\n  },\n  \"type\": \"payment_intent.succeeded\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/webhooks/payment/stripe", "host": ["{{base_url}}"], "path": ["api", "v1", "webhooks", "payment", "stripe"]}, "description": "Handle Stripe payment webhooks for payment status updates."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Webhook processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('processed');", "});"], "type": "text/javascript"}}]}, {"name": "Payment Webhook (PayPal)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "PayPal-Transmission-Id", "value": "sample_transmission_id", "description": "PayPal webhook transmission ID"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"WH-**********\",\n  \"event_version\": \"1.0\",\n  \"create_time\": \"2024-01-01T12:00:00Z\",\n  \"resource_type\": \"payment\",\n  \"event_type\": \"PAYMENT.CAPTURE.COMPLETED\",\n  \"summary\": \"Payment completed\",\n  \"resource\": {\n    \"id\": \"PAY-**********\",\n    \"state\": \"approved\",\n    \"amount\": {\n      \"total\": \"20.00\",\n      \"currency\": \"USD\"\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/webhooks/payment/paypal", "host": ["{{base_url}}"], "path": ["api", "v1", "webhooks", "payment", "paypal"]}, "description": "Handle PayPal payment webhooks for payment status updates."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Webhook processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('processed');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "⭐ Reviews & Ratings", "description": "Product reviews, ratings, and feedback management", "item": [{"name": "Get Product Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}/reviews?page=1&limit=10&rating=5&verified=true&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}", "reviews"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of reviews per page"}, {"key": "rating", "value": "5", "description": "Filter by rating (1-5)", "disabled": true}, {"key": "verified", "value": "true", "description": "Filter by verified purchase", "disabled": true}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, rating, helpful_count)", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)", "disabled": true}]}, "description": "Get reviews for a specific product with filtering and pagination options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains reviews data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Rating Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}/rating", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}", "rating"]}, "description": "Get rating summary for a product including average rating and rating distribution."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains rating data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('average_rating');", "    pm.expect(responseJson.data).to.have.property('total_reviews');", "});"], "type": "text/javascript"}}]}, {"name": "Create Product Review", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{first_product_id}}\",\n  \"order_id\": \"{{order_id}}\",\n  \"rating\": 5,\n  \"title\": \"Excellent product!\",\n  \"comment\": \"This product exceeded my expectations. Great quality and fast delivery. Highly recommended!\",\n  \"images\": [\n    \"https://example.com/review-image1.jpg\",\n    \"https://example.com/review-image2.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/reviews", "host": ["{{base_url}}"], "path": ["api", "v1", "reviews"]}, "description": "Create a new product review. User must have purchased the product to leave a review."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Review created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('rating');", "    ", "    // Store review ID for future tests", "    pm.environment.set('review_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Get User Reviews", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/reviews/user?page=1&limit=10&rating=5&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "reviews", "user"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "10", "description": "Number of reviews per page"}, {"key": "rating", "value": "5", "description": "Filter by rating (1-5)", "disabled": true}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, rating)", "disabled": true}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)", "disabled": true}]}, "description": "Get all reviews written by the current user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user reviews', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Update Review", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 4,\n  \"title\": \"Updated review title\",\n  \"comment\": \"Updated review comment with more details. The product quality is still good but I found some minor issues.\",\n  \"images\": [\n    \"https://example.com/updated-review-image1.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/reviews/{{review_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "reviews", "{{review_id}}"]}, "description": "Update an existing review. Only the review author can update their review."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Review updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data.title).to.include('Updated');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Review", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/reviews/{{review_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "reviews", "{{review_id}}"]}, "description": "Delete a review. Only the review author can delete their review."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Review deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}]}, {"name": "❤️ Wishlist", "description": "User wishlist management", "item": [{"name": "Get User Wishlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/wishlist?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of items per page"}]}, "description": "Get current user's wishlist with pagination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains wishlist data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Add Product to Wishlist", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/wishlist/items", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist", "items"]}, "description": "Add a product to user's wishlist."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Product added to wishlist', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('added');", "});"], "type": "text/javascript"}}]}, {"name": "Remove Product from Wishlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/wishlist/items/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist", "items", "{{product_id}}"]}, "description": "Remove a product from user's wishlist."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product removed from wishlist', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('removed');", "});"], "type": "text/javascript"}}]}, {"name": "Check Wishlist Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/wishlist/items/{{product_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist", "items", "{{product_id}}", "status"]}, "description": "Check if a specific product is in user's wishlist."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains wishlist status', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('in_wishlist');", "    pm.expect(responseJson.data.in_wishlist).to.be.a('boolean');", "});"], "type": "text/javascript"}}]}, {"name": "Clear Wishlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/wishlist/clear", "host": ["{{base_url}}"], "path": ["api", "v1", "wishlist", "clear"]}, "description": "Clear all items from user's wishlist."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Wishlist cleared successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('cleared');", "});"], "type": "text/javascript"}}]}]}, {"name": "📍 Addresses", "description": "User address management", "item": [{"name": "Get All Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/addresses", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses"]}, "description": "Get all addresses for the current user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains addresses', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Create Address", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"shipping\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"company\": \"Tech Corp\",\n  \"address1\": \"123 Main Street\",\n  \"address2\": \"Apt 4B\",\n  \"city\": \"New York\",\n  \"state\": \"NY\",\n  \"zip_code\": \"10001\",\n  \"country\": \"US\",\n  \"phone\": \"+**********\",\n  \"is_default\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/addresses", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses"]}, "description": "Create a new address for the user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Address created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('address_line_1');", "    ", "    // Store address ID for future tests", "    pm.environment.set('address_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"billing\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"company\": \"Updated Tech Corp\",\n  \"address1\": \"456 Updated Street\",\n  \"address2\": \"Suite 10\",\n  \"city\": \"Boston\",\n  \"state\": \"MA\",\n  \"zip_code\": \"02101\",\n  \"country\": \"US\",\n  \"phone\": \"+1987654321\",\n  \"is_default\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/addresses/{{address_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", "{{address_id}}"]}, "description": "Update an existing address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Address updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data.company).to.include('Updated');", "});"], "type": "text/javascript"}}]}, {"name": "Get Address by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/addresses/{{address_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", "{{address_id}}"]}, "description": "Get a specific address by ID."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains address data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('address_line_1');", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON> Default Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/addresses/{{address_id}}/default", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", "{{address_id}}", "default"]}, "description": "Set an address as the default address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Address set as default successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data.is_default).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/addresses/{{address_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses", "{{address_id}}"]}, "description": "Delete an address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Address deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}]}, {"name": "🎫 Coupons & Promotions", "description": "Discount coupons and promotional campaigns", "item": [{"name": "🔐 Admin Coupon Management", "description": "Admin-only coupon management endpoints (requires admin authentication)", "item": [{"name": "Create Coupon", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"SAVE20\",\n  \"name\": \"20% Off Summer Sale\",\n  \"description\": \"Get 20% off on all summer items\",\n  \"type\": \"percentage\",\n  \"value\": 20.0,\n  \"max_discount\": 50.0,\n  \"min_order_amount\": 100.0,\n  \"usage_limit\": 1000,\n  \"usage_limit_per_user\": 1,\n  \"applicability\": \"all\",\n  \"applicable_product_ids\": [],\n  \"applicable_category_ids\": [],\n  \"starts_at\": \"2024-06-01T00:00:00Z\",\n  \"expires_at\": \"2024-08-31T23:59:59Z\",\n  \"is_active\": true,\n  \"is_stackable\": false,\n  \"exclude_sale_items\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/coupons", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "coupons"]}, "description": "Create a new coupon. Requires admin authentication."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Coupon created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('code');", "    pm.expect(responseJson.data.code).to.eql('SAVE20');", "    ", "    // Store coupon ID for other tests", "    pm.environment.set('coupon_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "List Coupons", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/coupons?page=1&limit=20&is_active=true", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "coupons"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}]}, "description": "Get paginated list of all coupons."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Coupons list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Coupon", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/coupons/{{coupon_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "coupons", "{{coupon_id}}"]}, "description": "Get detailed information about a specific coupon."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Coupon details returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('code');", "});"], "type": "text/javascript"}}]}, {"name": "Update Coupon", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated 25% Off Summer Sale\",\n  \"description\": \"Updated: Get 25% off on all summer items\",\n  \"value\": 25.0,\n  \"max_discount\": 75.0,\n  \"usage_limit\": 1500,\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/coupons/{{coupon_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "coupons", "{{coupon_id}}"]}, "description": "Update an existing coupon. All fields are optional."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Coupon updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('value');", "    pm.expect(responseJson.data.value).to.eql(25.0);", "});"], "type": "text/javascript"}}]}, {"name": "Delete Coupon", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/coupons/{{coupon_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "coupons", "{{coupon_id}}"]}, "description": "Delete a coupon permanently."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Coupon deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "📁 File Management", "description": "File upload, management, and serving", "item": [{"name": "📋 File Upload Operations", "description": "File upload endpoints for different user types", "item": [{"name": "Upload Image (User)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload (JPG, PNG, GIF, WebP, max 5MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "image"]}, "description": "Upload an image file for authenticated users. Supports JPG, PNG, GIF, WebP formats with max size of 5MB."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson).to.have.property('fileName');", "    pm.expect(responseJson).to.have.property('fileSize');", "    pm.expect(responseJson).to.have.property('message');", "    ", "    // Store file info for other tests", "    pm.environment.set('uploaded_file_id', responseJson.id);", "    pm.environment.set('uploaded_file_url', responseJson.url);", "});", "", "pm.test('File URL is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.url).to.include('/uploads/');", "});"], "type": "text/javascript"}}]}, {"name": "Upload Image (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload (JPG, PNG, GIF, WebP, max 5MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/admin/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "upload", "image"]}, "description": "Upload an image file for admin users. Requires admin authentication. Used for product images, category images, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Admin file uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson.url).to.include('/uploads/admin/');", "    ", "    // Store admin file info", "    pm.environment.set('admin_file_id', responseJson.id);", "});"], "type": "text/javascript"}}]}, {"name": "Upload Image (Public)", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload (JPG, PNG, GIF, WebP, max 5MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/public/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "public", "upload", "image"]}, "description": "Upload an image file without authentication. Used for guest reviews, comments, etc."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Public file uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson.url).to.include('/uploads/public/');", "    ", "    // Store public file info", "    pm.environment.set('public_file_id', responseJson.id);", "});"], "type": "text/javascript"}}]}, {"name": "Upload Document (User)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Document file to upload (PDF, DOC, DOCX, TXT, max 10MB)"}]}, "url": {"raw": "{{base_url}}/api/v1/upload/document", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "document"]}, "description": "Upload a document file for authenticated users. Supports PDF, DOC, DOCX, TXT formats with max size of 10MB."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Document uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(response<PERSON><PERSON>).to.have.property('url');", "    pm.expect(responseJson.contentType).to.match(/application|text/);", "    ", "    // Store document info", "    pm.environment.set('uploaded_document_id', responseJson.id);", "});"], "type": "text/javascript"}}]}]}, {"name": "📋 File Management Operations", "description": "File retrieval and management endpoints", "item": [{"name": "Get File Upload Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}"]}, "description": "Get detailed information about a specific uploaded file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File info retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('fileName');", "    pm.expect(responseJson.data).to.have.property('originalName');", "    pm.expect(responseJson.data).to.have.property('fileSize');", "    pm.expect(responseJson.data).to.have.property('contentType');", "    pm.expect(responseJson.data).to.have.property('url');", "});"], "type": "text/javascript"}}]}, {"name": "Get All File Uploads", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/files?page=1&limit=20&category=images&upload_type=user", "host": ["{{base_url}}"], "path": ["api", "v1", "files"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of files per page"}, {"key": "category", "value": "images", "description": "Filter by file category (images, documents)"}, {"key": "upload_type", "value": "user", "description": "Filter by upload type (admin, user, public)"}]}, "description": "Get a paginated list of all uploaded files with optional filtering."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Files list retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Delete File", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/files/{{uploaded_file_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "files", "{{uploaded_file_id}}"]}, "description": "Delete an uploaded file. This will remove both the file record and the physical file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}]}, {"name": "📋 Public File Access", "description": "Public file access endpoints", "item": [{"name": "Get Public File", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/public/files/{{public_file_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "public", "files", "{{public_file_id}}"]}, "description": "Get a public file by ID. No authentication required."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Public file retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data.url).to.include('/uploads/public/');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🔔 Notifications", "description": "User notifications and preferences", "item": []}, {"name": "🔍 Search & Filtering", "description": "Advanced search, filtering, and autocomplete", "item": [{"name": "Full Text Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/search?q=laptop&category=electronics&min_price=500&max_price=2000&page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "search"], "query": [{"key": "q", "value": "laptop", "description": "Search query"}, {"key": "category", "value": "electronics", "description": "Category filter"}, {"key": "min_price", "value": "500", "description": "Minimum price filter"}, {"key": "max_price", "value": "2000", "description": "Maximum price filter"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Results per page"}]}, "description": "Perform full-text search across products with filters."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Search results returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data.products).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Enhanced Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/search/enhanced?q=gaming laptop&filters[brand]=dell,hp&filters[ram]=16GB&sort_by=price&sort_order=asc", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "enhanced"], "query": [{"key": "q", "value": "gaming laptop", "description": "Search query"}, {"key": "filters[brand]", "value": "dell,hp", "description": "Brand filters"}, {"key": "filters[ram]", "value": "16GB", "description": "RAM filter"}, {"key": "sort_by", "value": "price", "description": "Sort field"}, {"key": "sort_order", "value": "asc", "description": "Sort order"}]}, "description": "Enhanced search with advanced filtering and sorting."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Enhanced search results returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data).to.have.property('facets');", "});"], "type": "text/javascript"}}]}, {"name": "Get Search Suggestions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/search/suggestions?q=lap&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "suggestions"], "query": [{"key": "q", "value": "lap", "description": "Partial search query"}, {"key": "limit", "value": "10", "description": "Number of suggestions"}]}, "description": "Get search suggestions based on partial query."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Search suggestions returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🎯 Recommendations", "description": "Product recommendations and personalization", "item": []}, {"name": "🚚 Shipping & Inventory", "description": "Shipping methods, tracking, and inventory management", "item": []}, {"name": "🔔 Notifications", "description": "User notifications and real-time messaging", "item": [{"name": "Get User Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications?page=1&limit=15&unread_only=false", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "15", "description": "Number of notifications per page"}, {"key": "unread_only", "value": "false", "description": "Show only unread notifications", "disabled": true}]}, "description": "Get user's notifications with pagination and filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains notifications', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Get Unread Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/count", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "count"]}, "description": "Get count of unread notifications for the user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains unread count', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data.count).to.be.a('number');", "});"], "type": "text/javascript"}}]}, {"name": "Mark Notification as Read", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/{{notification_id}}/read", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "{{notification_id}}", "read"]}, "description": "Mark a specific notification as read."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Notification marked as read', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('read');", "});"], "type": "text/javascript"}}]}, {"name": "<PERSON> as <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/read-all", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "read-all"]}, "description": "Mark all notifications as read for the current user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('All notifications marked as read', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('All notifications');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Notification", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/{{notification_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "{{notification_id}}"]}, "description": "Delete a specific notification."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Notification deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('deleted');", "});"], "type": "text/javascript"}}]}, {"name": "Get Notification Preferences", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "preferences"]}, "description": "Get user's notification preferences and settings."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains preferences', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('email_notifications');", "    pm.expect(responseJson.data).to.have.property('push_notifications');", "});"], "type": "text/javascript"}}]}, {"name": "Update Notification Preferences", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email_notifications\": true,\n  \"push_notifications\": false,\n  \"sms_notifications\": false,\n  \"order_updates\": true,\n  \"promotional_emails\": false,\n  \"security_alerts\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/notifications/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "preferences"]}, "description": "Update user's notification preferences."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Preferences updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('email_notifications');", "});"], "type": "text/javascript"}}]}]}, {"name": "📊 Analytics", "description": "Analytics events and metrics tracking", "item": []}, {"name": "🔧 Admin Panel", "description": "Administrative operations and management", "item": [{"name": "📊 Dashboard & Analytics", "description": "Admin dashboard and system analytics", "item": [{"name": "Get Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard?period=30d&include_charts=true", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard"], "query": [{"key": "period", "value": "30d", "description": "Time period for dashboard data (7d, 30d, 90d, 1y)"}, {"key": "include_charts", "value": "true", "description": "Include chart data in response"}]}, "description": "Get comprehensive admin dashboard data including overview statistics, charts, and recent activity."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Dashboard data retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('overview');", "    pm.expect(responseJson.data.overview).to.have.property('totalRevenue');", "    pm.expect(responseJson.data.overview).to.have.property('totalOrders');", "    pm.expect(responseJson.data.overview).to.have.property('totalUsers');", "    pm.expect(responseJson.data.overview).to.have.property('totalProducts');", "});"], "type": "text/javascript"}}]}, {"name": "Get System Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/dashboard/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "dashboard", "stats"]}, "description": "Get detailed system statistics and performance metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('System stats retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('systemHealth');", "});"], "type": "text/javascript"}}]}, {"name": "Get Sales Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/sales?period=30d&group_by=day", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "sales"], "query": [{"key": "period", "value": "30d", "description": "Time period for analytics (7d, 30d, 90d, 1y)"}, {"key": "group_by", "value": "day", "description": "Group data by (day, week, month)"}]}, "description": "Get detailed sales analytics and metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Sales analytics retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('totalRevenue');", "    pm.expect(responseJson.data).to.have.property('totalOrders');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/products?limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "products"], "query": [{"key": "limit", "value": "10", "description": "Number of top products to return"}]}, "description": "Get product performance analytics and metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product analytics retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('topProducts');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/analytics/users?period=30d", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "users"], "query": [{"key": "period", "value": "30d", "description": "Time period for user analytics"}]}, "description": "Get user behavior analytics and metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User analytics retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('totalUsers');", "    pm.expect(responseJson.data).to.have.property('newUsers');", "});"], "type": "text/javascript"}}]}]}, {"name": "👥 User Management", "description": "Admin user management operations", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/users?page=1&limit=25&role=customer&status=active&search=john&sort_by=created_at&sort_order=desc", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "25", "description": "Number of users per page"}, {"key": "role", "value": "customer", "description": "Filter by user role (customer, admin, moderator)"}, {"key": "status", "value": "active", "description": "Filter by user status (active, inactive, suspended)"}, {"key": "search", "value": "john", "description": "Search by name or email"}, {"key": "sort_by", "value": "created_at", "description": "Sort field (created_at, name, email, last_login)"}, {"key": "sort_order", "value": "desc", "description": "Sort order (asc, desc)"}]}, "description": "Get paginated list of all users with advanced filtering and sorting options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Users list retrieved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('users');", "    pm.expect(responseJson.data.users).to.be.an('array');", "    pm.expect(responseJson.data).to.have.property('pagination');", "    ", "    // Store first user ID for other tests", "    if (responseJson.data.users.length > 0) {", "        pm.environment.set('admin_user_id', responseJson.data.users[0].id);", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "📊 Reports & System", "description": "Report generation and system management", "item": [{"name": "Generate Report", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"sales\",\n  \"format\": \"pdf\",\n  \"date_from\": \"2024-01-01\",\n  \"date_to\": \"2024-12-31\",\n  \"filters\": {\n    \"category_id\": null,\n    \"product_id\": null\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/reports/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports", "generate"]}, "description": "Generate various types of reports (sales, inventory, users, etc.)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Report generation initiated', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('report_id');", "    ", "    // Store report ID for future tests", "    pm.environment.set('report_id', responseJson.data.report_id);", "});"], "type": "text/javascript"}}]}, {"name": "Get Reports List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/reports?page=1&limit=20&type=sales&status=completed", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "type", "value": "sales", "description": "Report type filter", "disabled": true}, {"key": "status", "value": "completed", "description": "Report status filter", "disabled": true}]}, "description": "Get list of generated reports with filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains reports list', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    pm.expect(responseJson).to.have.property('pagination');", "});"], "type": "text/javascript"}}]}, {"name": "Download Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/reports/{{report_id}}/download", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports", "{{report_id}}", "download"]}, "description": "Download a generated report file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is a file', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/');", "});"], "type": "text/javascript"}}]}, {"name": "Get System Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/system/logs?level=error&limit=100&from=2024-01-01", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "system", "logs"], "query": [{"key": "level", "value": "error", "description": "Log level filter (debug, info, warn, error)", "disabled": true}, {"key": "limit", "value": "100", "description": "Number of log entries"}, {"key": "from", "value": "2024-01-01", "description": "Start date for logs", "disabled": true}]}, "description": "Get system logs with filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains logs', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "⚙️ Settings Management", "description": "Admin settings and configuration management", "item": [{"name": "Get General Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/settings/general", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "settings", "general"]}, "description": "Get general store settings and configuration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('General settings returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('store_name');", "    pm.expect(responseJson.data).to.have.property('currency');", "});"], "type": "text/javascript"}}]}, {"name": "Update General Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"store_name\": \"My Awesome Store\",\n  \"store_description\": \"The best online store for all your needs\",\n  \"store_email\": \"<EMAIL>\",\n  \"store_phone\": \"******-123-4567\",\n  \"store_address\": \"123 Main St, City, State 12345\",\n  \"currency\": \"USD\",\n  \"timezone\": \"America/New_York\",\n  \"language\": \"en\",\n  \"date_format\": \"YYYY-MM-DD\",\n  \"time_format\": \"HH:mm:ss\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/settings/general", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "settings", "general"]}, "description": "Update general store settings and configuration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Settings updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Get Payment Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/settings/payment", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "settings", "payment"]}, "description": "Get payment gateway settings and configuration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Payment settings returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('default_currency');", "    pm.expect(responseJson.data).to.have.property('stripe_enabled');", "});"], "type": "text/javascript"}}]}, {"name": "Update Payment Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"default_currency\": \"USD\",\n  \"accepted_currencies\": [\"USD\", \"EUR\", \"GBP\"],\n  \"stripe_enabled\": true,\n  \"stripe_public_key\": \"pk_test_...\",\n  \"stripe_secret_key\": \"sk_test_...\",\n  \"paypal_enabled\": true,\n  \"paypal_client_id\": \"paypal_client_id_here\",\n  \"paypal_client_secret\": \"paypal_client_secret_here\",\n  \"paypal_sandbox\": true,\n  \"cash_on_delivery\": true,\n  \"bank_transfer\": false,\n  \"bank_details\": {\n    \"account_name\": \"My Store Account\",\n    \"account_number\": \"**********\",\n    \"bank_name\": \"Example Bank\",\n    \"routing_number\": \"*********\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/settings/payment", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "settings", "payment"]}, "description": "Update payment gateway settings and configuration."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Payment settings updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "👥 User Management", "description": "Admin user management and operations", "item": [{"name": "List Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/users?page=1&limit=20&role=user&status=active", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "role", "value": "user", "description": "Filter by user role"}, {"key": "status", "value": "active", "description": "Filter by user status"}]}, "description": "Get paginated list of all users with filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Users list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('users');", "    pm.expect(responseJson.data.users).to.be.an('array');", "    pm.expect(responseJson.data).to.have.property('total');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"suspended\",\n  \"reason\": \"Violation of terms of service\",\n  \"notes\": \"User reported for inappropriate behavior\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/{{user_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "{{user_id}}", "status"]}, "description": "Update user account status (active, suspended, banned)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User status updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Update User Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"role\": \"moderator\",\n  \"reason\": \"Promoted to moderator role\",\n  \"effective_date\": \"2024-01-01T00:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/{{user_id}}/role", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "{{user_id}}", "role"]}, "description": "Update user role (user, moderator, admin)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User role updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Get User Activity", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/users/{{user_id}}/activity?limit=50&days=30", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "{{user_id}}", "activity"], "query": [{"key": "limit", "value": "50", "description": "Number of activities to return"}, {"key": "days", "value": "30", "description": "Number of days to look back"}]}, "description": "Get user activity history and logs."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('User activity returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('activities');", "    pm.expect(responseJson.data.activities).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Send User Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"title\": \"Important Account Update\",\n  \"message\": \"Your account has been updated. Please review the changes.\",\n  \"type\": \"info\",\n  \"data\": {\n    \"action_url\": \"/account/settings\",\n    \"priority\": \"high\",\n    \"expires_at\": \"2024-12-31T23:59:59Z\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/notification", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "notification"]}, "description": "Send notification to a specific user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Notification sent successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "📁 File Management", "description": "Admin file management and uploads", "item": [{"name": "List Files", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/files?page=1&limit=20&type=image&category=product", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "files"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "type", "value": "image", "description": "Filter by file type"}, {"key": "category", "value": "product", "description": "Filter by file category"}]}, "description": "Get paginated list of all uploaded files."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Files list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('files');", "    pm.expect(responseJson.data.files).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get File Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/files/{{file_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "files", "{{file_id}}"]}, "description": "Get detailed information about a specific file."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File details returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('filename');", "});"], "type": "text/javascript"}}]}, {"name": "Delete File", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/files/{{file_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "files", "{{file_id}}"]}, "description": "Delete a file permanently from the system."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('File deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Admin Upload Image", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload"}, {"key": "category", "value": "product", "type": "text", "description": "File category"}, {"key": "alt_text", "value": "Product image", "type": "text", "description": "Alternative text for image"}]}, "url": {"raw": "{{base_url}}/api/v1/admin/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "upload", "image"]}, "description": "Upload image file with admin privileges."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Image uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('id');", "    ", "    // Store file ID for other tests", "    pm.environment.set('file_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Admin Upload Document", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Document file to upload"}, {"key": "category", "value": "documentation", "type": "text", "description": "File category"}, {"key": "description", "value": "Admin documentation file", "type": "text", "description": "File description"}]}, "url": {"raw": "{{base_url}}/api/v1/admin/upload/document", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "upload", "document"]}, "description": "Upload document file with admin privileges."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Document uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('url');", "    pm.expect(responseJson.data).to.have.property('id');", "});"], "type": "text/javascript"}}]}]}, {"name": "📦 Order Management", "description": "Admin order management and operations", "item": [{"name": "List Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/orders?page=1&limit=20&status=pending&user_id={{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "orders"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "status", "value": "pending", "description": "Filter by order status"}, {"key": "user_id", "value": "{{user_id}}", "description": "Filter by user ID"}]}, "description": "Get paginated list of all orders with filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Orders list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('orders');", "    pm.expect(responseJson.data.orders).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"processing\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/orders/{{order_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "orders", "{{order_id}}", "status"]}, "description": "Update order status with optional notes."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Order status updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Update Shipping Info", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tracking_number\": \"1Z999AA**********\",\n  \"carrier\": \"UPS\",\n  \"shipping_method\": \"Ground\",\n  \"tracking_url\": \"https://www.ups.com/track?tracknum=1Z999AA**********\",\n  \"estimated_delivery\": \"2024-02-20T15:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/orders/{{order_id}}/shipping", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "orders", "{{order_id}}", "shipping"]}, "description": "Update shipping information for an order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Shipping info updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Add Order Note", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"note\": \"Customer requested expedited shipping\",\n  \"is_public\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/orders/{{order_id}}/notes", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "orders", "{{order_id}}", "notes"]}, "description": "Add a note to an order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Order note added successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔄 Returns Management", "description": "Admin returns and refunds management", "item": [{"name": "Create Return", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"items\": [\n    {\n      \"product_id\": \"{{product_id}}\",\n      \"quantity\": 1\n    }\n  ],\n  \"reason\": \"defective\",\n  \"description\": \"Product arrived damaged\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/returns", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "returns"]}, "description": "Create a return request for an order."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Return created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    ", "    // Store return ID for other tests", "    if (responseJson.data && responseJson.data.id) {", "        pm.environment.set('return_id', responseJson.data.id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "List Returns", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/returns?page=1&limit=20&status=requested", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "returns"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "status", "value": "requested", "description": "Filter by return status"}]}, "description": "Get paginated list of all returns."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Returns list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('returns');", "    pm.expect(responseJson.data.returns).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Process Return", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"approved\",\n  \"notes\": \"Return approved, refund will be processed\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/returns/{{return_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "returns", "{{return_id}}", "status"]}, "description": "Process a return request (approve/reject)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Return processed successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "📊 Reports Management", "description": "Admin reports generation and management", "item": [{"name": "Generate Report", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"sales\",\n  \"format\": \"excel\",\n  \"date_from\": \"2024-01-01T00:00:00Z\",\n  \"date_to\": \"2024-01-31T23:59:59Z\",\n  \"filters\": {\n    \"status\": \"completed\",\n    \"payment_status\": \"paid\",\n    \"min_amount\": 50.00\n  },\n  \"group_by\": \"day\",\n  \"created_by\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/reports/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports", "generate"]}, "description": "Generate various types of reports (sales, inventory, users, etc.)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Report generated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    ", "    // Store report ID for other tests", "    if (responseJson.data && responseJson.data.report_id) {", "        pm.environment.set('report_id', responseJson.data.report_id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "List Reports", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/reports?page=1&limit=20&type=sales", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "reports"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "type", "value": "sales", "description": "Filter by report type"}]}, "description": "Get paginated list of generated reports."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Reports list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('reports');", "    pm.expect(responseJson.data.reports).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "💾 Backup Management", "description": "System backup and restore operations", "item": [{"name": "List Backups", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/backup", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "backup"]}, "description": "Get list of all system backups."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Backups list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('backups');", "    pm.expect(responseJson.data.backups).to.be.an('array');", "    ", "    // Store first backup ID for other tests", "    if (responseJson.data.backups.length > 0) {", "        pm.environment.set('backup_id', responseJson.data.backups[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Create Backup", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Manual Backup - {{$timestamp}}\",\n  \"description\": \"Manual backup created for testing\",\n  \"type\": \"full\",\n  \"include_files\": true,\n  \"include_database\": true,\n  \"compression\": \"gzip\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/backup", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "backup"]}, "description": "Create a new system backup."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Backup created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    ", "    // Store backup ID for other tests", "    if (responseJson.data && responseJson.data.id) {", "        pm.environment.set('backup_id', responseJson.data.id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Backup Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/backup/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "backup", "stats"]}, "description": "Get backup statistics and storage information."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Backup stats returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('total_backups');", "    pm.expect(responseJson.data).to.have.property('total_size');", "});"], "type": "text/javascript"}}]}, {"name": "Restore Backup", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"backup_id\": \"{{backup_id}}\",\n  \"restore_database\": true,\n  \"restore_files\": false,\n  \"confirmation\": \"I understand this will overwrite current data\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/backup/restore", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "backup", "restore"]}, "description": "Restore system from a backup."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Backup restore initiated', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "👥 Bulk User Operations", "description": "Bulk operations for user management", "item": [{"name": "Bulk Update Users", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [\"{{user_id}}\", \"{{second_user_id}}\"],\n  \"updates\": {\n    \"first_name\": \"Updated\",\n    \"last_name\": \"User\",\n    \"phone\": \"+**********\",\n    \"status\": \"active\",\n    \"role\": \"customer\",\n    \"is_active\": true,\n    \"membership_tier\": \"silver\",\n    \"security_level\": \"standard\"\n  },\n  \"reason\": \"Bulk user profile update for compliance\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/bulk/update", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "bulk", "update"]}, "description": "Update multiple users at once."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk update completed', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('updated_count');", "});"], "type": "text/javascript"}}]}, {"name": "Bulk Delete Users", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [\"{{inactive_user_id}}\"],\n  \"reason\": \"Account cleanup - inactive users\",\n  \"force\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/bulk/delete", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "bulk", "delete"]}, "description": "Delete multiple users at once."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk delete completed', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('deleted_count');", "});"], "type": "text/javascript"}}]}, {"name": "Bulk Activate Users", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [\"{{user_id}}\", \"{{second_user_id}}\"],\n  \"reason\": \"Bulk activation after verification\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/bulk/activate", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "bulk", "activate"]}, "description": "Activate multiple users at once."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk activation completed', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('activated_count');", "});"], "type": "text/javascript"}}]}, {"name": "Bulk Deactivate Users", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [\"{{inactive_user_id}}\"],\n  \"reason\": \"Bulk deactivation for policy violation\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/bulk/deactivate", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "bulk", "deactivate"]}, "description": "Deactivate multiple users at once."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk deactivation completed', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('deactivated_count');", "});"], "type": "text/javascript"}}]}, {"name": "Bulk Update User Roles", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [\"{{user_id}}\", \"{{second_user_id}}\"],\n  \"role\": \"premium_customer\",\n  \"reason\": \"Bulk role upgrade for loyalty program\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/bulk/roles", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "bulk", "roles"]}, "description": "Update roles for multiple users at once."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk role update completed', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('updated_count');", "});"], "type": "text/javascript"}}]}, {"name": "Send Bulk Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [\"{{user_id}}\", \"{{second_user_id}}\"],\n  \"title\": \"System Maintenance Notice\",\n  \"message\": \"We will be performing scheduled maintenance on our system tonight from 2 AM to 4 AM EST.\",\n  \"type\": \"info\",\n  \"data\": {\n    \"maintenance_window\": \"2024-02-15 02:00 - 04:00 EST\",\n    \"affected_services\": [\"checkout\", \"payments\"]\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/bulk/notification", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "bulk", "notification"]}, "description": "Send notifications to multiple users at once."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk notifications sent', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('sent_count');", "});"], "type": "text/javascript"}}]}, {"name": "Send Bulk Email", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_ids\": [\"{{user_id}}\", \"{{second_user_id}}\"],\n  \"subject\": \"Important Account Update\",\n  \"body\": \"Dear valued customer,\\n\\nWe have updated our terms of service. Please review the changes at your earliest convenience.\\n\\nBest regards,\\nThe Team\",\n  \"template\": \"account_update\",\n  \"data\": {\n    \"company_name\": \"Our Store\",\n    \"support_email\": \"<EMAIL>\",\n    \"update_date\": \"2024-02-15\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/bulk/email", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "bulk", "email"]}, "description": "Send emails to multiple users at once."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Bulk emails sent', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('sent_count');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "👥 Moderator Panel", "description": "Moderator operations and content management", "item": []}, {"name": "🌐 WebSocket", "description": "Real-time notifications and WebSocket connections", "item": []}, {"name": "⚙️ System & Utilities", "description": "Health checks, migrations, system management, and utilities", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check system health and status."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('System is healthy', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('status');", "    pm.expect(responseJson.status).to.equal('healthy');", "});"], "type": "text/javascript"}}]}, {"name": "API Version Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/version", "host": ["{{base_url}}"], "path": ["api", "v1", "version"]}, "description": "Get API version and build information."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains version info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('version');", "    pm.expect(responseJson).to.have.property('build_time');", "});"], "type": "text/javascript"}}]}, {"name": "Get Shipping Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/methods", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "methods"]}, "description": "Get available shipping methods and rates."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping methods', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Calculate Shipping Cost", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"destination\": {\n    \"country\": \"US\",\n    \"state\": \"CA\",\n    \"city\": \"San Francisco\",\n    \"postal_code\": \"94102\"\n  },\n  \"items\": [\n    {\n      \"product_id\": \"{{product_id}}\",\n      \"quantity\": 2,\n      \"weight\": 1.5,\n      \"dimensions\": {\n        \"length\": 10,\n        \"width\": 8,\n        \"height\": 6\n      }\n    }\n  ],\n  \"shipping_method\": \"standard\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/shipping/rates", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "rates"]}, "description": "Calculate shipping cost for items to a specific destination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping cost', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('cost');", "    pm.expect(responseJson.data).to.have.property('estimated_delivery');", "});"], "type": "text/javascript"}}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "// Set common headers and handle authentication", "", "// Set Content-Type for JSON requests", "if (pm.request.method !== 'GET' && pm.request.body && pm.request.body.mode === 'raw') {", "    pm.request.headers.add({", "        key: 'Content-Type',", "        value: 'application/json'", "    });", "}", "", "// Add Authorization header if jwt_token exists", "const token = pm.environment.get('jwt_token') || pm.globals.get('jwt_token');", "if (token) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + token", "    });", "}", "", "// Add session ID for guest operations", "const sessionId = pm.environment.get('session_id') || pm.globals.get('session_id');", "if (sessionId && !token) {", "    pm.request.headers.add({", "        key: 'X-Session-ID',", "        value: sessionId", "    });", "}", "", "// Generate session ID if not exists", "if (!sessionId) {", "    const newSessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);", "    pm.environment.set('session_id', newSessionId);", "    pm.request.headers.add({", "        key: 'X-Session-ID',", "        value: newSessionId", "    });", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "// Handle common response patterns and token management", "", "// Log response for debugging", "console.log('Response Status:', pm.response.code);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "// Handle authentication responses", "if (pm.response.code === 200 || pm.response.code === 201) {", "    try {", "        const responseJson = pm.response.json();", "        ", "        // Store JWT token from login/register responses", "        if (responseJson.data && responseJson.data.token) {", "            pm.environment.set('jwt_token', responseJson.data.token);", "            console.log('JWT token stored');", "        }", "        ", "        // Store refresh token", "        if (responseJson.data && responseJson.data.refresh_token) {", "            pm.environment.set('refresh_token', responseJson.data.refresh_token);", "            console.log('Refresh token stored');", "        }", "        ", "        // Store user ID", "        if (responseJson.data && responseJson.data.user && responseJson.data.user.id) {", "            pm.environment.set('user_id', responseJson.data.user.id);", "            console.log('User ID stored');", "        }", "        ", "        // Store created resource IDs", "        if (responseJson.data && responseJson.data.id) {", "            const requestName = pm.info.requestName.toLowerCase();", "            if (requestName.includes('product')) {", "                pm.environment.set('last_product_id', responseJson.data.id);", "            } else if (requestName.includes('category')) {", "                pm.environment.set('last_category_id', responseJson.data.id);", "            } else if (requestName.includes('order')) {", "                pm.environment.set('last_order_id', responseJson.data.id);", "            }", "        }", "    } catch (e) {", "        console.log('Response is not JSON or error parsing:', e.message);", "    }", "}", "", "// Handle 401 Unauthorized - clear tokens", "if (pm.response.code === 401) {", "    pm.environment.unset('jwt_token');", "    pm.environment.unset('refresh_token');", "    console.log('Tokens cleared due to 401 response');", "}", "", "// Basic response validation", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid status code', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 202, 204, 400, 401, 403, 404, 422, 500]);", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "api_version", "value": "v1", "type": "string"}, {"name": "🔍 Search & Recommendations", "description": "Advanced search, recommendations, and product discovery endpoints", "item": [{"name": "📋 Search Operations", "description": "Search functionality and autocomplete", "item": [{"name": "Get Search Suggestions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/suggestions?q=iphone&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "suggestions"], "query": [{"key": "q", "value": "iphone", "description": "Search query"}, {"key": "limit", "value": "10", "description": "Number of suggestions"}]}, "description": "Get search suggestions and autocomplete results for product search."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains suggestions', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Popular Searches", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/popular-searches?limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "popular-searches"], "query": [{"key": "limit", "value": "20", "description": "Number of popular searches to return"}]}, "description": "Get list of popular search terms and trending queries."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains popular searches', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Search History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/products/search-history?limit=50", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "search-history"], "query": [{"key": "limit", "value": "50", "description": "Number of search history items"}]}, "description": "Get user's search history (requires authentication)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains search history', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🎯 Recommendations", "description": "Product recommendation endpoints", "item": [{"name": "Get Personalized Recommendations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/recommendations/personalized?limit=12", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "personalized"], "query": [{"key": "limit", "value": "12", "description": "Number of recommendations"}]}, "description": "Get personalized product recommendations for authenticated user."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains recommendations', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data.products).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Related Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/recommendations/related/{{first_product_id}}?limit=6", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "related", "{{first_product_id}}"], "query": [{"key": "limit", "value": "6", "description": "Number of related products"}]}, "description": "Get products related to a specific product."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains related products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data.products).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Frequently Bought Together", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/recommendations/frequently-bought-together/{{first_product_id}}?limit=5", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "frequently-bought-together", "{{first_product_id}}"], "query": [{"key": "limit", "value": "5", "description": "Number of products"}]}, "description": "Get products frequently bought together with a specific product."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains frequently bought products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data.products).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Trending Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/recommendations/trending?period=week&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "trending"], "query": [{"key": "period", "value": "week", "description": "Time period: day, week, month"}, {"key": "limit", "value": "20", "description": "Number of trending products"}]}, "description": "Get trending products for a specific time period."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains trending products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data.products).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "👥 Customer Management", "description": "Admin customer analytics and segmentation", "item": [{"name": "Search Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/customers/search?query=john&status=active&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "customers", "search"], "query": [{"key": "query", "value": "john", "description": "Search query for customer name/email"}, {"key": "status", "value": "active", "description": "Filter by customer status"}, {"key": "limit", "value": "20", "description": "Number of customers to return"}]}, "description": "Search customers by name, email, or other criteria."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer search results returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('customers');", "    pm.expect(responseJson.data.customers).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Customer Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/customers/analytics?period=30d", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "customers", "analytics"], "query": [{"key": "period", "value": "30d", "description": "Analytics period (7d, 30d, 90d)"}]}, "description": "Get comprehensive customer analytics and insights."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Customer analytics returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('analytics');", "    pm.expect(responseJson.data.analytics).to.have.property('total_customers');", "});"], "type": "text/javascript"}}]}, {"name": "Get High Value Customers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/customers/high-value?limit=50&min_value=1000", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "customers", "high-value"], "query": [{"key": "limit", "value": "50", "description": "Number of customers to return"}, {"key": "min_value", "value": "1000", "description": "Minimum customer lifetime value"}]}, "description": "Get list of high-value customers based on lifetime value."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('High value customers returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('customers');", "    pm.expect(responseJson.data.customers).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔄 Product Comparison", "description": "Product comparison and analysis endpoints", "item": [{"name": "Create Product Comparison", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Session-ID", "value": "{{session_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"product_ids\": [\n    \"{{first_product_id}}\",\n    \"{{created_product_id}}\"\n  ],\n  \"name\": \"Laptop Comparison\",\n  \"description\": \"Comparing different laptop models\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/comparisons", "host": ["{{base_url}}"], "path": ["api", "v1", "comparisons"]}, "description": "Create a new product comparison set."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains comparison data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('products');", "    ", "    // Store comparison ID for other tests", "    pm.environment.set('comparison_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Comparison", "request": {"method": "GET", "header": [{"key": "X-Session-ID", "value": "{{session_id}}"}], "url": {"raw": "{{base_url}}/api/v1/comparisons/{{comparison_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "comparisons", "{{comparison_id}}"]}, "description": "Get details of a specific product comparison."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains comparison details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data.products).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Compare Products Matrix", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/comparisons/compare?product_ids={{first_product_id}},{{created_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "comparisons", "compare"], "query": [{"key": "product_ids", "value": "{{first_product_id}},{{created_product_id}}", "description": "Comma-separated product IDs"}]}, "description": "Get detailed comparison matrix for multiple products."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains comparison matrix', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('products');", "    pm.expect(responseJson.data).to.have.property('attributes');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Product Comparison", "request": {"method": "DELETE", "header": [{"key": "X-Session-ID", "value": "{{session_id}}"}], "url": {"raw": "{{base_url}}/api/v1/comparisons/{{comparison_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "comparisons", "{{comparison_id}}"]}, "description": "Delete a product comparison."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Comparison deleted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}]}, {"name": "📊 Analytics & Tracking", "description": "Analytics, event tracking, and metrics endpoints", "item": [{"name": "Track Custom Event", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"event_name\": \"product_view\",\n  \"event_category\": \"engagement\",\n  \"properties\": {\n    \"product_id\": \"{{first_product_id}}\",\n    \"category\": \"electronics\",\n    \"price\": 999.99,\n    \"source\": \"search\"\n  },\n  \"user_agent\": \"PostmanRuntime/7.32.3\",\n  \"ip_address\": \"127.0.0.1\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/analytics/events", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "events"]}, "description": "Track a custom analytics event with properties."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Event tracked successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('tracked');", "});"], "type": "text/javascript"}}]}, {"name": "Track Page View", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page_url\": \"/products/{{first_product_id}}\",\n  \"page_title\": \"Product Details - iPhone 15 Pro\",\n  \"referrer\": \"/search?q=iphone\",\n  \"user_agent\": \"PostmanRuntime/7.32.3\",\n  \"session_duration\": 45000,\n  \"metadata\": {\n    \"category\": \"product_detail\",\n    \"product_id\": \"{{first_product_id}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/analytics/page-views", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "analytics", "page-views"]}, "description": "Track a page view event with metadata."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Page view tracked successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('tracked');", "});"], "type": "text/javascript"}}]}, {"name": "Get Sales Metrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/analytics/sales?start_date=2024-01-01&end_date=2024-12-31&granularity=month", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "sales"], "query": [{"key": "start_date", "value": "2024-01-01", "description": "Start date for metrics"}, {"key": "end_date", "value": "2024-12-31", "description": "End date for metrics"}, {"key": "granularity", "value": "month", "description": "Data granularity: day, week, month"}]}, "description": "Get sales metrics and analytics data."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains sales metrics', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('total_sales');", "    pm.expect(responseJson.data).to.have.property('order_count');", "});"], "type": "text/javascript"}}]}, {"name": "Get Product Metrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/analytics/products?limit=50", "host": ["{{base_url}}"], "path": ["api", "v1", "analytics", "products"], "query": [{"key": "limit", "value": "50", "description": "Number of products to analyze"}]}, "description": "Get product performance metrics and analytics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains product metrics', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}, {"name": "🚚 Shipping & Logistics", "description": "Shipping methods, rates, and tracking endpoints", "item": [{"name": "Get Shipping Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/methods", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "methods"]}, "description": "Get all available shipping methods and their details."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping methods', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "    ", "    // Store first shipping method ID", "    if (responseJson.data && responseJson.data.length > 0) {", "        pm.environment.set('shipping_method_id', responseJson.data[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Calculate Shipping Rates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"origin\": {\n    \"address1\": \"123 Warehouse St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"zip_code\": \"10001\",\n    \"country\": \"USA\"\n  },\n  \"destination\": {\n    \"address1\": \"456 Customer Ave\",\n    \"city\": \"Los Angeles\",\n    \"state\": \"CA\",\n    \"zip_code\": \"90210\",\n    \"country\": \"USA\"\n  },\n  \"packages\": [\n    {\n      \"weight\": 2.5,\n      \"dimensions\": {\n        \"length\": 12,\n        \"width\": 8,\n        \"height\": 4\n      },\n      \"value\": 299.99\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/shipping/rates", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "rates"]}, "description": "Calculate shipping rates for packages between origin and destination."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping rates', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('rates');", "    pm.expect(responseJson.data.rates).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Validate Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"address1\": \"1600 Amphitheatre Parkway\",\n  \"city\": \"Mountain View\",\n  \"state\": \"CA\",\n  \"zip_code\": \"94043\",\n  \"country\": \"USA\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/shipping/validate-address", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "validate-address"]}, "description": "Validate and normalize a shipping address."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Address validation response', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('is_valid');", "    pm.expect(responseJson.data).to.have.property('normalized_address');", "});"], "type": "text/javascript"}}]}, {"name": "Track Shipment", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/track/1Z999AA**********", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "track", "1Z999AA**********"]}, "description": "Track a shipment using tracking number."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains tracking info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('tracking_number');", "    pm.expect(responseJson.data).to.have.property('status');", "});"], "type": "text/javascript"}}]}, {"name": "Get Shipping Zones", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/shipping/zones", "host": ["{{base_url}}"], "path": ["api", "v1", "shipping", "zones"]}, "description": "Get all shipping zones and their configurations."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains shipping zones', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}, {"name": "Adjust Stock", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\",\n  \"warehouse_id\": \"{{warehouse_id}}\",\n  \"quantity_delta\": -5,\n  \"reason\": \"Damaged goods\",\n  \"notes\": \"Items damaged during inspection\",\n  \"batch_number\": \"BATCH001\",\n  \"expiry_date\": \"2024-12-31T23:59:59Z\",\n  \"adjusted_by\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/inventory/adjust", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "inventory", "adjust"]}, "description": "Adjust stock quantity for a product in a warehouse."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Stock adjusted successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});"], "type": "text/javascript"}}]}, {"name": "Record Movement", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\",\n  \"warehouse_id\": \"{{warehouse_id}}\",\n  \"type\": \"in\",\n  \"reason\": \"Purchase order received\",\n  \"quantity\": 100,\n  \"unit_cost\": 25.50,\n  \"reference_type\": \"purchase_order\",\n  \"reference_id\": \"{{purchase_order_id}}\",\n  \"notes\": \"Received from supplier ABC\",\n  \"batch_number\": \"BATCH002\",\n  \"expiry_date\": \"2025-06-30T23:59:59Z\",\n  \"created_by\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/inventory/movements", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "inventory", "movements"]}, "description": "Record an inventory movement (in/out/transfer)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Movement recorded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Get Stock Alerts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/inventory/alerts?type=low_stock&status=active&warehouse_id={{warehouse_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "inventory", "alerts"], "query": [{"key": "type", "value": "low_stock", "description": "Alert type"}, {"key": "status", "value": "active", "description": "Alert status"}, {"key": "warehouse_id", "value": "{{warehouse_id}}", "description": "Filter by warehouse"}]}, "description": "Get stock alerts for low inventory levels."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Stock alerts returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('alerts');", "    pm.expect(responseJson.data.alerts).to.be.an('array');", "});"], "type": "text/javascript"}}]}]}]}, {"name": "🎫 Coupons & Promotions", "description": "Coupon validation and promotion management endpoints", "item": [{"name": "Validate Coupon", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"SAVE20\",\n  \"cart_total\": 299.99,\n  \"user_id\": \"{{user_id}}\",\n  \"product_ids\": [\n    \"{{first_product_id}}\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/coupons/validate", "host": ["{{base_url}}"], "path": ["api", "v1", "coupons", "validate"]}, "description": "Validate a coupon code and get discount information."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains coupon validation', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('is_valid');", "    pm.expect(responseJson.data).to.have.property('discount_amount');", "    ", "    // Store coupon info if valid", "    if (responseJson.data.is_valid) {", "        pm.environment.set('valid_coupon_code', 'SAVE20');", "        pm.environment.set('discount_amount', responseJson.data.discount_amount);", "    }", "});"], "type": "text/javascript"}}]}]}, {"name": "📦 Inventory Management", "description": "Admin inventory tracking and stock management endpoints", "item": [{"name": "List Inventories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/inventory?page=1&limit=20&warehouse_id={{warehouse_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "inventory"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "20", "description": "Items per page"}, {"key": "warehouse_id", "value": "{{warehouse_id}}", "description": "Filter by warehouse ID"}]}, "description": "Get paginated list of all inventory items."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Inventories list returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('inventories');", "    pm.expect(responseJson.data.inventories).to.be.an('array');", "    ", "    // Store first inventory ID for other tests", "    if (responseJson.data.inventories.length > 0) {", "        pm.environment.set('inventory_id', responseJson.data.inventories[0].id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Update Product Stock", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\",\n  \"warehouse_id\": \"{{warehouse_id}}\",\n  \"quantity_on_hand\": 150,\n  \"reorder_level\": 20,\n  \"max_stock_level\": 500,\n  \"min_stock_level\": 10,\n  \"average_cost\": 25.50,\n  \"last_cost\": 24.00,\n  \"updated_by\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/inventory/{{inventory_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "inventory", "{{inventory_id}}"]}, "description": "Update stock quantity and inventory settings for a product."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Stock updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Get Low Stock Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/inventory/low-stock?limit=50", "host": ["{{base_url}}"], "path": ["api", "v1", "inventory", "low-stock"], "query": [{"key": "limit", "value": "50", "description": "Number of products to return"}]}, "description": "Get list of products with low stock levels."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains low stock products', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Reserve Inventory", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{first_product_id}}\",\n  \"quantity\": 2,\n  \"reservation_type\": \"order\",\n  \"reference_id\": \"{{order_id}}\",\n  \"expires_at\": \"2024-12-31T23:59:59Z\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/inventory/reserve", "host": ["{{base_url}}"], "path": ["api", "v1", "inventory", "reserve"]}, "description": "Reserve inventory for an order or cart."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Inventory reserved successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('reservation_id');", "    ", "    // Store reservation ID", "    pm.environment.set('reservation_id', responseJson.data.reservation_id);", "});"], "type": "text/javascript"}}]}]}, {"name": "🌐 WebSocket & Real-time", "description": "WebSocket connections and real-time communication endpoints", "item": [{"name": "WebSocket Notifications Connection", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/ws/notifications?token={{jwt_token}}", "host": ["{{base_url}}"], "path": ["api", "v1", "ws", "notifications"], "query": [{"key": "token", "value": "{{jwt_token}}", "description": "JWT token for WebSocket authentication"}]}, "description": "Establish WebSocket connection for real-time notifications. This endpoint upgrades HTTP connection to WebSocket."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('WebSocket upgrade successful', function () {", "    // WebSocket connections return 101 Switching Protocols", "    // or may return connection info", "    pm.expect([101, 200]).to.include(pm.response.code);", "});", "", "pm.test('WebSocket connection established', function () {", "    // Check for WebSocket upgrade headers or connection info", "    const upgradeHeader = pm.response.headers.get('Upgrade');", "    if (upgradeHeader) {", "        pm.expect(upgradeHeader.toLowerCase()).to.include('websocket');", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get WebSocket Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/ws/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "ws", "stats"]}, "description": "Get WebSocket connection statistics and metrics."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains WebSocket stats', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('total_connections');", "    pm.expect(responseJson.data).to.have.property('active_users');", "});"], "type": "text/javascript"}}]}, {"name": "Get Connected Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/ws/users", "host": ["{{base_url}}"], "path": ["api", "v1", "ws", "users"]}, "description": "Get list of currently connected WebSocket users."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains connected users', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Send Test Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Notification\",\n  \"message\": \"This is a test WebSocket notification\",\n  \"type\": \"info\",\n  \"data\": {\n    \"action\": \"test\",\n    \"timestamp\": \"2024-01-01T12:00:00Z\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/ws/test/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "ws", "test", "{{user_id}}"]}, "description": "Send a test notification to a specific user via WebSocket."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Test notification sent', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('sent');", "});"], "type": "text/javascript"}}]}, {"name": "Broadcast Test Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"System Announcement\",\n  \"message\": \"This is a broadcast message to all connected users\",\n  \"type\": \"announcement\",\n  \"priority\": \"high\",\n  \"data\": {\n    \"broadcast_id\": \"broadcast_001\",\n    \"timestamp\": \"2024-01-01T12:00:00Z\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/ws/broadcast", "host": ["{{base_url}}"], "path": ["api", "v1", "ws", "broadcast"]}, "description": "Broadcast a notification to all connected WebSocket users."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Broadcast notification sent', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('broadcast');", "});"], "type": "text/javascript"}}]}]}, {"name": "🔧 System Management", "description": "System logs, audit trails, security monitoring, and maintenance operations", "item": [{"name": "Get System Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/system/logs?level=error&service=api&search=database&limit=50&offset=0&date_from=2024-01-01&date_to=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "system", "logs"], "query": [{"key": "level", "value": "error", "description": "Log level filter (debug, info, warn, error)"}, {"key": "service", "value": "api", "description": "Filter by service name"}, {"key": "search", "value": "database", "description": "Search term in log messages"}, {"key": "limit", "value": "50", "description": "Number of logs to return"}, {"key": "offset", "value": "0", "description": "Offset for pagination"}, {"key": "date_from", "value": "2024-01-01", "description": "Start date for log filtering"}, {"key": "date_to", "value": "2024-01-31", "description": "End date for log filtering"}]}, "description": "Get system logs with filtering options."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('System logs returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('logs');", "    pm.expect(responseJson.data.logs).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Audit Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/system/audit?action=user_update&user_id={{user_id}}&resource=users&limit=25&offset=0&date_from=2024-01-01&date_to=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "system", "audit"], "query": [{"key": "action", "value": "user_update", "description": "Filter by action type"}, {"key": "user_id", "value": "{{user_id}}", "description": "Filter by user ID"}, {"key": "resource", "value": "users", "description": "Filter by resource type"}, {"key": "limit", "value": "25", "description": "Number of audit logs to return"}, {"key": "offset", "value": "0", "description": "Offset for pagination"}, {"key": "date_from", "value": "2024-01-01", "description": "Start date for audit log filtering"}, {"key": "date_to", "value": "2024-01-31", "description": "End date for audit log filtering"}]}, "description": "Get audit logs for system actions and changes."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Audit logs returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('audit_logs');", "    pm.expect(responseJson.data.audit_logs).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Suspicious Activity", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/security/suspicious-activity?severity=high&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "security", "suspicious-activity"], "query": [{"key": "severity", "value": "high", "description": "Filter by severity level"}, {"key": "limit", "value": "20", "description": "Number of activities to return"}]}, "description": "Get suspicious login activities and security alerts."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Suspicious activities returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('activities');", "    pm.expect(responseJson.data.activities).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "Get Security Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/security/report?period=30d", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "security", "report"], "query": [{"key": "period", "value": "30d", "description": "Report period (7d, 30d, 90d)"}]}, "description": "Get comprehensive security report."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Security report returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('summary');", "    pm.expect(responseJson.data).to.have.property('threats_detected');", "});"], "type": "text/javascript"}}]}]}, {"name": "👨‍💼 Moderator Operations", "description": "Moderator-level operations for content and product management", "item": [{"name": "Create Product (Moderator)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Moderator Created Product\",\n  \"description\": \"This product was created by a moderator for testing purposes\",\n  \"short_description\": \"Moderator test product\",\n  \"sku\": \"MOD-TEST-001\",\n  \"slug\": \"moderator-test-product\",\n  \"meta_title\": \"Moderator Test Product - Best Quality\",\n  \"meta_description\": \"High-quality test product created by moderator\",\n  \"keywords\": \"moderator, test, quality\",\n  \"featured\": false,\n  \"visibility\": \"visible\",\n  \"price\": 199.99,\n  \"compare_price\": 249.99,\n  \"cost_price\": 120.00,\n  \"stock\": 50,\n  \"low_stock_threshold\": 5,\n  \"track_quantity\": true,\n  \"allow_backorder\": false,\n  \"weight\": 1.5,\n  \"dimensions\": {\n    \"length\": 10,\n    \"width\": 8,\n    \"height\": 3\n  },\n  \"requires_shipping\": true,\n  \"shipping_class\": \"standard\",\n  \"tax_class\": \"taxable\",\n  \"country_of_origin\": \"USA\",\n  \"category_id\": \"{{category_id}}\",\n  \"brand_id\": \"{{brand_id}}\",\n  \"images\": [\n    {\n      \"url\": \"https://example.com/product-image-1.jpg\",\n      \"alt_text\": \"Product main image\",\n      \"is_primary\": true,\n      \"sort_order\": 1\n    }\n  ],\n  \"tags\": [\"moderator\", \"test\", \"electronics\"],\n  \"attributes\": [\n    {\n      \"attribute_id\": \"{{color_attribute_id}}\",\n      \"term_id\": null,\n      \"value\": \"Black\",\n      \"position\": 1\n    },\n    {\n      \"attribute_id\": \"{{material_attribute_id}}\",\n      \"term_id\": null,\n      \"value\": \"Plastic\",\n      \"position\": 2\n    }\n  ],\n  \"status\": \"active\",\n  \"product_type\": \"simple\",\n  \"is_digital\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/moderator/products", "host": ["{{base_url}}"], "path": ["api", "v1", "moderator", "products"]}, "description": "Create a new product with moderator privileges."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Product created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('sku');", "    ", "    // Store moderator created product ID", "    pm.environment.set('moderator_product_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Update Product (Moderator)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Moderator Product\",\n  \"description\": \"This product has been updated by a moderator\",\n  \"price\": 179.99,\n  \"stock\": 75,\n  \"status\": \"active\",\n  \"featured\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/moderator/products/{{moderator_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "moderator", "products", "{{moderator_product_id}}"]}, "description": "Update an existing product with moderator privileges."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data.name).to.include('Updated');", "});"], "type": "text/javascript"}}]}, {"name": "Patch Product (Moderator)", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"price\": 159.99,\n  \"featured\": false,\n  \"meta_description\": \"Partially updated product description\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/moderator/products/{{moderator_product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "moderator", "products", "{{moderator_product_id}}"]}, "description": "Partially update a product with moderator privileges."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product patched successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "});"], "type": "text/javascript"}}]}, {"name": "Update Product Stock (Moderator)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"stock\": 100,\n  \"low_stock_threshold\": 10,\n  \"track_quantity\": true,\n  \"allow_backorder\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/moderator/products/{{moderator_product_id}}/stock", "host": ["{{base_url}}"], "path": ["api", "v1", "moderator", "products", "{{moderator_product_id}}", "stock"]}, "description": "Update product stock levels with moderator privileges."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Stock updated successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('updated');", "});"], "type": "text/javascript"}}]}, {"name": "Upload Image (Moderator)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Image file to upload"}, {"key": "alt_text", "value": "Moderator uploaded image", "type": "text"}, {"key": "category", "value": "product", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/moderator/upload/image", "host": ["{{base_url}}"], "path": ["api", "v1", "moderator", "upload", "image"]}, "description": "Upload an image file with moderator privileges."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Image uploaded successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('url');", "    ", "    // Store moderator uploaded file info", "    pm.environment.set('moderator_file_id', responseJson.data.id);", "    pm.environment.set('moderator_file_url', responseJson.data.url);", "});"], "type": "text/javascript"}}]}]}]}