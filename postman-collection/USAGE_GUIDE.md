# 📖 Postman Collection Usage Guide

## 🚀 Quick Start

### 1. **Import Collection và Environment**
```bash
1. Import "Ecommerce-API-Collection.postman_collection.json"
2. Import "Ecommerce-API-Environment.postman_environment.json"
3. Select environment trong Postman
```

### 2. **Set Base URL**
```bash
Trong Environment variables:
base_url = http://localhost:8080  # hoặc URL server của bạn
```

### 3. **Authentication Flow**
```bash
1. Run "Register" để tạo account mới
2. Hoặc run "Login" với existing account
3. JWT token sẽ tự động được lưu vào environment
4. Tất cả authenticated requests sẽ tự động sử dụng token
```

## 📁 **Collection Structure**

### 🔐 **Authentication & Session Management**
- **Register**: Tạo account mới
- **Login**: Đăng nhập và nhận JWT token
- **Refresh <PERSON>ken**: Làm mới access token
- **Logout**: Đăng xuất khỏi session hiện tại
- **Password Reset**: Quên mật khẩu workflow

### 👤 **User Management**
- **Profile Operations**: Get/Update profile, change password
- **Preferences**: Theme, language, privacy settings
- **Activity Tracking**: Login history, account statistics

### 🛍️ **Shopping Experience**
- **Product Discovery**: Browse, search, filter products
- **Shopping Cart**: Add/remove items, apply coupons
- **Wishlist**: Save favorite products
- **Reviews**: Read/write product reviews

### 🔍 **Search & Recommendations**
- **Full Text Search**: Advanced search với filters
- **Autocomplete**: Smart suggestions
- **Recommendations**: Personalized product suggestions
- **Trending**: Popular searches và products

### 📍 **Address & Payment Management**
- **Addresses**: CRUD operations cho shipping/billing addresses
- **Payment Methods**: Manage credit cards, bank accounts
- **Default Settings**: Set preferred address/payment method

### 🔔 **Notifications**
- **Get Notifications**: Paginated notification list
- **Mark as Read**: Individual hoặc bulk operations
- **Preferences**: Configure notification settings

### ⚖️ **Product Comparison**
- **Add to Compare**: So sánh multiple products
- **Comparison Matrix**: Detailed feature comparison
- **Share Comparison**: Generate shareable links

### 🌐 **Real-time Features**
- **WebSocket Info**: Connection endpoints
- **Live Updates**: Product stock, order status
- **Push Notifications**: Real-time alerts

### 🏪 **Store Operations**
- **Orders**: Create, track, manage orders
- **Shipping**: Calculate rates, track shipments
- **Coupons**: Apply discounts và promotions
- **Returns**: Handle return requests

### 🔧 **Admin Operations**
- **Product Management**: CRUD operations
- **Inventory**: Stock management, movements, alerts
- **User Management**: Admin user operations
- **Reports**: Generate analytics reports
- **System Settings**: Configure store settings

## 🎯 **Common Workflows**

### **Customer Registration & Shopping**
```bash
1. POST /auth/register
2. GET /products (browse products)
3. GET /search?q=laptop (search products)
4. POST /cart/items (add to cart)
5. POST /addresses (add shipping address)
6. POST /payment-methods (add payment method)
7. POST /orders (create order)
8. GET /orders/{id} (track order)
```

### **Admin Product Management**
```bash
1. POST /auth/login (admin login)
2. POST /admin/products (create product)
3. POST /admin/categories (create category)
4. POST /admin/inventory/movements (record stock)
5. GET /admin/inventory/alerts (check alerts)
6. POST /admin/inventory/reports (generate report)
```

### **Search & Discovery**
```bash
1. GET /search/autocomplete?q=lap (get suggestions)
2. GET /search?q=laptop&category=electronics (search)
3. GET /recommendations/personalized (get recommendations)
4. POST /recommendations/track (track interactions)
```

## 🔧 **Environment Variables**

### **Authentication**
```bash
jwt_token - Access token for authenticated requests
admin_token - Admin access token
refresh_token - Token for refreshing access
session_id - Session ID for guest operations
```

### **Resource IDs**
```bash
user_id - Current user ID
product_id - Last created/selected product
category_id - Last created/selected category
order_id - Last created order
address_id - Last created address
payment_method_id - Last created payment method
```

### **Search & Pagination**
```bash
search_query - Current search term
partial_query - Partial query for autocomplete
page - Current page number
limit - Results per page
sort_by - Sort criteria
min_price, max_price - Price filters
```

### **System**
```bash
base_url - API base URL
warehouse_id - Default warehouse
websocket_url - WebSocket connection URL
```

## 🧪 **Testing Features**

### **Automatic Token Management**
- JWT tokens tự động được extract và store
- Admin tokens được detect và sử dụng cho admin endpoints
- Session IDs được generate cho guest operations

### **Smart Authentication**
- Tự động detect endpoint type (public/authenticated/admin)
- Apply đúng authentication headers
- Handle token expiration

### **Comprehensive Validation**
- Response time validation (< 5 seconds)
- Status code validation
- JSON structure validation
- Pagination validation
- Error response validation

### **Data Extraction**
- Tự động extract và store resource IDs
- Pagination metadata tracking
- Error details logging

## 🚨 **Troubleshooting**

### **Authentication Issues**
```bash
Problem: 401 Unauthorized
Solution: 
1. Check jwt_token trong environment
2. Run Login request để refresh token
3. Verify token chưa expired
```

### **Missing Environment Variables**
```bash
Problem: {{variable}} not resolved
Solution:
1. Check environment được select
2. Run prerequisite requests để populate variables
3. Manually set required variables
```

### **Request Failures**
```bash
Problem: Request fails với unexpected error
Solution:
1. Check Console logs cho detailed error
2. Verify request body structure
3. Check required headers
4. Validate endpoint URL
```

## 📊 **Performance Tips**

### **Efficient Testing**
- Use Collection Runner cho batch testing
- Set appropriate delays between requests
- Use environment variables để avoid hardcoding
- Group related tests together

### **Rate Limiting**
- Monitor X-RateLimit-Remaining header
- Add delays cho high-volume testing
- Use different API keys cho parallel testing

### **Data Management**
- Clean up test data regularly
- Use unique identifiers cho test data
- Reset environment variables between test runs

## 🎯 **Best Practices**

### **Request Organization**
- Group related requests trong folders
- Use descriptive request names
- Add comprehensive descriptions
- Include example responses

### **Environment Management**
- Use separate environments cho dev/staging/prod
- Keep sensitive data trong environment variables
- Document all required variables
- Use meaningful variable names

### **Testing Strategy**
- Test happy path scenarios first
- Include error case testing
- Validate all response fields
- Test edge cases và boundary conditions

### **Documentation**
- Keep request descriptions up-to-date
- Include example request/response bodies
- Document any special requirements
- Add troubleshooting notes

## 🏆 **Advanced Features**

### **Automated Workflows**
- Use Pre-request Scripts cho setup
- Chain requests using extracted data
- Implement retry logic cho flaky tests
- Generate dynamic test data

### **Custom Validation**
- Write custom test assertions
- Validate business logic
- Check data consistency
- Implement performance benchmarks

### **Integration Testing**
- Test complete user journeys
- Validate cross-service interactions
- Check data flow between endpoints
- Verify system state changes

---

**Happy Testing!** 🚀✨

For support or questions, check the implementation documentation hoặc contact the development team.
