{"id": "ecom-clean-environment", "name": "🛍️ Ecommerce API - Clean Environment", "values": [{"key": "base_url", "value": "http://localhost:8080", "description": "Base URL for the API server", "enabled": true}, {"key": "api_version", "value": "v1", "description": "API version", "enabled": true}, {"key": "jwt_token", "value": "", "description": "JWT access token for authenticated requests", "enabled": true}, {"key": "admin_token", "value": "", "description": "JWT token for admin operations", "enabled": true}, {"key": "refresh_token", "value": "", "description": "Refresh token for token renewal", "enabled": true}, {"key": "session_id", "value": "", "description": "Session ID for guest operations", "enabled": true}, {"key": "user_id", "value": "", "description": "Current authenticated user ID", "enabled": true}, {"key": "user_email", "value": "", "description": "Current authenticated user email", "enabled": true}, {"key": "user_role", "value": "", "description": "Current authenticated user role", "enabled": true}, {"key": "product_id", "value": "", "description": "Product ID for testing", "enabled": true}, {"key": "category_id", "value": "", "description": "Category ID for testing", "enabled": true}, {"key": "brand_id", "value": "", "description": "Brand ID for testing", "enabled": true}, {"key": "variant_id", "value": "", "description": "Product variant ID for testing", "enabled": true}, {"key": "cart_item_id", "value": "", "description": "Cart item ID for testing", "enabled": true}, {"key": "order_id", "value": "", "description": "Order ID for testing", "enabled": true}, {"key": "order_number", "value": "", "description": "Order number for tracking", "enabled": true}, {"key": "address_id", "value": "", "description": "Address ID for testing", "enabled": true}, {"key": "payment_method_id", "value": "", "description": "Payment method ID for testing", "enabled": true}, {"key": "notification_id", "value": "", "description": "Notification ID for testing", "enabled": true}, {"key": "comparison_id", "value": "", "description": "Product comparison ID for testing", "enabled": true}, {"key": "warehouse_id", "value": "warehouse_main", "description": "Default warehouse ID", "enabled": true}, {"key": "target_warehouse_id", "value": "warehouse_secondary", "description": "Target warehouse for transfers", "enabled": true}, {"key": "search_query", "value": "laptop", "description": "Default search query for testing", "enabled": true}, {"key": "partial_query", "value": "lap", "description": "Partial query for autocomplete testing", "enabled": true}, {"key": "selected_suggestion", "value": "laptop gaming", "description": "Selected suggestion for tracking", "enabled": true}, {"key": "search_filters", "value": "{\"brand\":[\"apple\",\"dell\"],\"rating\":[4,5],\"features\":[\"ssd\",\"touchscreen\"]}", "description": "JSON search filters", "enabled": true}, {"key": "page", "value": "1", "description": "Current page number for pagination", "enabled": true}, {"key": "limit", "value": "20", "description": "Results per page", "enabled": true}, {"key": "sort_by", "value": "relevance", "description": "Sort criteria (relevance, price_asc, price_desc, name, created_at)", "enabled": true}, {"key": "min_price", "value": "100", "description": "Minimum price filter", "enabled": true}, {"key": "max_price", "value": "2000", "description": "Maximum price filter", "enabled": true}, {"key": "current_page", "value": "", "description": "Current page from pagination response", "enabled": true}, {"key": "total_pages", "value": "", "description": "Total pages from pagination response", "enabled": true}, {"key": "total_items", "value": "", "description": "Total items from pagination response", "enabled": true}, {"key": "timestamp", "value": "", "description": "Current timestamp (auto-generated)", "enabled": true}, {"key": "random_email", "value": "", "description": "Random email for testing (auto-generated)", "enabled": true}, {"key": "random_string", "value": "", "description": "Random string for testing (auto-generated)", "enabled": true}, {"key": "registered_user_id", "value": "", "description": "ID of newly registered user", "enabled": true}, {"key": "registered_user_email", "value": "", "description": "Email of newly registered user", "enabled": true}, {"key": "token_expires_at", "value": "", "description": "JWT token expiration timestamp", "enabled": true}, {"key": "websocket_url", "value": "", "description": "WebSocket connection URL", "enabled": true}, {"key": "last_product_id", "value": "", "description": "Last created/accessed product ID", "enabled": true}, {"key": "last_category_id", "value": "", "description": "Last created/accessed category ID", "enabled": true}, {"key": "product_sku", "value": "", "description": "Product SKU for testing", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-23T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}