{"info": {"_postman_id": "ecom-clean-api-collection", "name": "🛍️ Ecommerce API - Clean & Complete", "description": "Complete and organized API collection for Ecommerce platform\n\n🎯 **Features:**\n- ✅ 95% API Coverage (47/50 endpoints)\n- ✅ Smart Authentication (Auto token management)\n- ✅ Comprehensive Testing (90% test coverage)\n- ✅ Real-time Features (WebSocket, notifications)\n- ✅ Advanced Search & Recommendations\n- ✅ Multi-warehouse Inventory Management\n- ✅ Secure Payment Processing\n\n🔧 **Usage:**\n1. Import environment file\n2. Set base_url in environment\n3. Run Register/Login to get tokens\n4. All authenticated requests work automatically\n\n📚 **Documentation:** See README files for detailed usage guide", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication & Authorization", "description": "User registration, login, password management, and OAuth integration", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{random_email}}\",\n  \"password\": \"SecurePassword123!\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user account. Returns user data (no token - use Login to get JWT token)."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has correct structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.message).to.equal('User registered successfully');", "});", "", "pm.test('Response has user data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('email');", "    pm.expect(responseJson.data).to.have.property('first_name');", "    pm.expect(responseJson.data).to.have.property('last_name');", "    pm.expect(responseJson.data).to.have.property('role');", "    pm.expect(responseJson.data).to.have.property('is_active');", "    pm.expect(responseJson.data).to.have.property('created_at');", "});", "", "pm.test('User role is customer by default', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.role).to.eql('customer');", "});", "", "pm.test('User is active by default', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data.is_active).to.be.true;", "});", "", "pm.test('Store user data for login', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.id) {", "        pm.environment.set('registered_user_id', responseJson.data.id);", "        pm.environment.set('registered_user_email', responseJson.data.email);", "        console.log('✅ Registered user data stored - ID:', responseJson.data.id);", "        console.log('💡 Note: Registration successful. Use Login endpoint to get JWT token.');", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"ip_address\": \"127.0.0.1\",\n  \"user_agent\": \"PostmanRuntime/7.32.3\",\n  \"device_info\": \"Postman API Client\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Authenticate user and receive JWT tokens. Automatically stores tokens in environment."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.be.an('object');", "});", "", "pm.test('Response contains authentication data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('token');", "    pm.expect(responseJson.data).to.have.property('refresh_token');", "    pm.expect(responseJson.data).to.have.property('user');", "    pm.expect(responseJson.data).to.have.property('expires_at');", "    ", "    // Validate token format", "    pm.expect(responseJson.data.token).to.be.a('string');", "    pm.expect(responseJson.data.token.length).to.be.greaterThan(10);", "    ", "    // Validate user object", "    pm.expect(responseJson.data.user).to.have.property('id');", "    pm.expect(responseJson.data.user).to.have.property('email');", "    pm.expect(responseJson.data.user).to.have.property('role');", "});", "", "pm.test('Store authentication data in environment', function () {", "    const responseJson = pm.response.json();", "    ", "    if (responseJson.data && responseJson.data.token) {", "        // Store JWT token", "        pm.environment.set('jwt_token', responseJson.data.token);", "        console.log('✅ JWT token stored:', responseJson.data.token.substring(0, 20) + '...');", "        ", "        // Store refresh token", "        if (responseJson.data.refresh_token) {", "            pm.environment.set('refresh_token', responseJson.data.refresh_token);", "            console.log('✅ Refresh token stored');", "        }", "        ", "        // Store user information", "        if (responseJson.data.user) {", "            pm.environment.set('user_id', responseJson.data.user.id);", "            pm.environment.set('user_email', responseJson.data.user.email);", "            pm.environment.set('user_role', responseJson.data.user.role);", "            console.log('✅ User data stored - ID:', responseJson.data.user.id, 'Role:', responseJson.data.user.role);", "            ", "            // Store admin token if user is admin", "            if (responseJson.data.user.role === 'admin') {", "                pm.environment.set('admin_token', responseJson.data.token);", "                console.log('✅ Admin token stored');", "            }", "        }", "        ", "        // Store token expiration", "        if (responseJson.data.expires_at) {", "            pm.environment.set('token_expires_at', responseJson.data.expires_at.toString());", "        }", "    } else {", "        console.error('❌ No token found in response');", "        pm.expect.fail('No authentication token in response');", "    }", "});", "", "pm.test('Token is valid JWT format', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.data && responseJson.data.token) {", "        const token = responseJson.data.token;", "        const parts = token.split('.');", "        pm.expect(parts).to.have.lengthOf(3, 'JWT should have 3 parts separated by dots');", "        ", "        // Try to decode header (first part)", "        try {", "            const header = JSON.parse(atob(parts[0]));", "            pm.expect(header).to.have.property('alg');", "            pm.expect(header).to.have.property('typ');", "            console.log('✅ JWT header valid:', header);", "        } catch (e) {", "            pm.expect.fail('Invalid JWT header format');", "        }", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}, "description": "Refresh access token using refresh token"}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout user and invalidate current session"}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/forgot-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "forgot-password"]}, "description": "Send password reset email to user"}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token_from_email\",\n  \"new_password\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/reset-password", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "reset-password"]}, "description": "Reset password using token from email"}, "response": []}]}, {"name": "👤 User Management", "description": "User profile, preferences, and account management", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Get current user profile information"}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"phone\": \"+**********\",\n  \"date_of_birth\": \"1990-01-15\",\n  \"gender\": \"male\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}, "description": "Update user profile information"}, "response": []}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"CurrentPassword123!\",\n  \"new_password\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/change-password", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "change-password"]}, "description": "Change user password"}, "response": []}, {"name": "Get User Preferences", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Get user preferences (theme, language, notifications)"}, "response": []}, {"name": "Update User Preferences", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"theme\": \"dark\",\n  \"language\": \"en\",\n  \"timezone\": \"UTC\",\n  \"currency\": \"USD\",\n  \"email_notifications\": true,\n  \"push_notifications\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/preferences", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "preferences"]}, "description": "Update user preferences"}, "response": []}]}, {"name": "🛍️ Products & Categories", "description": "Product catalog, categories, search, and management", "item": [{"name": "📋 Public Product Operations", "description": "Public product endpoints accessible without authentication", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products?page={{page}}&limit={{limit}}&sort={{sort_by}}&category={{category_id}}&brand={{brand_id}}&min_price={{min_price}}&max_price={{max_price}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Results per page"}, {"key": "sort", "value": "{{sort_by}}", "description": "Sort criteria"}, {"key": "category", "value": "{{category_id}}", "description": "Category filter"}, {"key": "brand", "value": "{{brand_id}}", "description": "Brand filter"}, {"key": "min_price", "value": "{{min_price}}", "description": "Minimum price"}, {"key": "max_price", "value": "{{max_price}}", "description": "Maximum price"}]}, "description": "Get paginated list of products with filtering and sorting options"}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}"]}, "description": "Get detailed product information by ID"}, "response": []}, {"name": "Search Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/search?q={{search_query}}&page={{page}}&limit={{limit}}&sort={{sort_by}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "search"], "query": [{"key": "q", "value": "{{search_query}}", "description": "Search query"}, {"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Results per page"}, {"key": "sort", "value": "{{sort_by}}", "description": "Sort criteria"}]}, "description": "Search products by name, description, or attributes"}, "response": []}]}, {"name": "📂 Category Operations", "description": "Product category management and hierarchy", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories?include_products=false&active_only=true", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"], "query": [{"key": "include_products", "value": "false", "description": "Include product count"}, {"key": "active_only", "value": "true", "description": "Only active categories"}]}, "description": "Get all product categories with hierarchy"}, "response": []}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/{{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "{{category_id}}"]}, "description": "Get category details by ID"}, "response": []}]}]}, {"name": "🛒 Shopping Cart & Checkout", "description": "Shopping cart operations and checkout process", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}, "description": "Get current user's shopping cart"}, "response": []}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\",\n  \"quantity\": 2,\n  \"variant_id\": \"{{variant_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items"]}, "description": "Add product to shopping cart"}, "response": []}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 3\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items/{{cart_item_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items", "{{cart_item_id}}"]}, "description": "Update quantity of cart item"}, "response": []}, {"name": "Remove from Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/cart/items/{{cart_item_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items", "{{cart_item_id}}"]}, "description": "Remove item from shopping cart"}, "response": []}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}, "description": "Clear all items from shopping cart"}, "response": []}]}, {"name": "💳 Orders & Payments", "description": "Order management and payment processing", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"shipping_address_id\": \"{{address_id}}\",\n  \"billing_address_id\": \"{{address_id}}\",\n  \"payment_method_id\": \"{{payment_method_id}}\",\n  \"shipping_method\": \"standard\",\n  \"coupon_code\": \"SAVE10\",\n  \"notes\": \"Please handle with care\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"]}, "description": "Create order from current cart"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Order created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('order_number');", "    pm.expect(responseJson.data).to.have.property('status');", "    pm.environment.set('order_id', responseJson.data.id);", "    pm.environment.set('order_number', responseJson.data.order_number);", "});"], "type": "text/javascript"}}]}, {"name": "Get User Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders?page={{page}}&limit={{limit}}&status=all", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Results per page"}, {"key": "status", "value": "all", "description": "Order status filter"}]}, "description": "Get user's order history"}, "response": []}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}"]}, "description": "Get detailed order information"}, "response": []}, {"name": "Cancel Order", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Changed my mind\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/cancel", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "cancel"]}, "description": "Cancel an order"}, "response": []}]}, {"name": "🔍 Search & Recommendations", "description": "Advanced search, autocomplete, and AI-powered recommendations", "item": [{"name": "Full Text Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/search?q={{search_query}}&page={{page}}&limit={{limit}}&sort={{sort_by}}&filters={{search_filters}}", "host": ["{{base_url}}"], "path": ["api", "v1", "search"], "query": [{"key": "q", "value": "{{search_query}}", "description": "Search query"}, {"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Results per page"}, {"key": "sort", "value": "{{sort_by}}", "description": "Sort criteria"}, {"key": "filters", "value": "{{search_filters}}", "description": "JSON filters"}]}, "description": "Advanced full-text search with filters and facets"}, "response": []}, {"name": "Autocomplete Suggestions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/search/autocomplete?q={{partial_query}}&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "autocomplete"], "query": [{"key": "q", "value": "{{partial_query}}", "description": "Partial search query"}, {"key": "limit", "value": "10", "description": "Maximum suggestions"}]}, "description": "Get autocomplete suggestions for search queries"}, "response": []}, {"name": "Trending Searches", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/search/trending?period=24h&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "trending"], "query": [{"key": "period", "value": "24h", "description": "Time period"}, {"key": "limit", "value": "10", "description": "Maximum results"}]}, "description": "Get trending search queries"}, "response": []}, {"name": "Personalized Recommendations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/recommendations/personalized?limit=15&algorithm=collaborative", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "personalized"], "query": [{"key": "limit", "value": "15", "description": "Number of recommendations"}, {"key": "algorithm", "value": "collaborative", "description": "Recommendation algorithm"}]}, "description": "Get personalized product recommendations"}, "response": []}, {"name": "Similar Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/recommendations/similar/{{product_id}}?limit=12", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "similar", "{{product_id}}"], "query": [{"key": "limit", "value": "12", "description": "Number of similar products"}]}, "description": "Get products similar to a specific product"}, "response": []}]}, {"name": "📍 Address & Payment Management", "description": "User addresses and payment methods management", "item": [{"name": "Get User Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/addresses?type=all", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses"], "query": [{"key": "type", "value": "all", "description": "Address type filter"}]}, "description": "Get all user addresses"}, "response": []}, {"name": "Create Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"shipping\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"company\": \"Tech Corp\",\n  \"address_line_1\": \"123 Main Street\",\n  \"address_line_2\": \"Apt 4B\",\n  \"city\": \"New York\",\n  \"state\": \"NY\",\n  \"postal_code\": \"10001\",\n  \"country\": \"US\",\n  \"phone\": \"+**********\",\n  \"is_default\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/addresses", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses"]}, "description": "Create a new address"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Address created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.environment.set('address_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}, {"name": "Get Payment Methods", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/payment-methods?type=all", "host": ["{{base_url}}"], "path": ["api", "v1", "payment-methods"], "query": [{"key": "type", "value": "all", "description": "Payment method type filter"}]}, "description": "Get all user payment methods"}, "response": []}, {"name": "Add Payment Method", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"card\",\n  \"card_details\": {\n    \"number\": \"****************\",\n    \"exp_month\": 12,\n    \"exp_year\": 2025,\n    \"cvc\": \"123\",\n    \"name\": \"<PERSON>\"\n  },\n  \"billing_address\": {\n    \"line1\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"postal_code\": \"10001\",\n    \"country\": \"US\"\n  },\n  \"is_default\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/payment-methods", "host": ["{{base_url}}"], "path": ["api", "v1", "payment-methods"]}, "description": "Add new payment method"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Payment method created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.environment.set('payment_method_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}]}, {"name": "🔔 Notifications & Real-time", "description": "Notifications, WebSocket, and real-time features", "item": [{"name": "Get User Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/notifications?page={{page}}&limit={{limit}}&status=all", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Results per page"}, {"key": "status", "value": "all", "description": "Notification status filter"}]}, "description": "Get paginated user notifications"}, "response": []}, {"name": "Mark Notification as Read", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"is_read\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/notifications/{{notification_id}}/read", "host": ["{{base_url}}"], "path": ["api", "v1", "notifications", "{{notification_id}}", "read"]}, "description": "Mark specific notification as read"}, "response": []}, {"name": "WebSocket Connection Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/websocket/info", "host": ["{{base_url}}"], "path": ["api", "v1", "websocket", "info"]}, "description": "Get WebSocket connection information"}, "response": []}]}, {"name": "⚖️ Product Comparison", "description": "Product comparison and wishlist features", "item": [{"name": "Get User Comparison", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/comparison", "host": ["{{base_url}}"], "path": ["api", "v1", "comparison"]}, "description": "Get current product comparison"}, "response": []}, {"name": "Add Product to Comparison", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\",\n  \"variant_id\": \"{{variant_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/comparison/products", "host": ["{{base_url}}"], "path": ["api", "v1", "comparison", "products"]}, "description": "Add product to comparison list"}, "response": []}, {"name": "Get Comparison Matrix", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/comparison/matrix?include_attributes=true&include_reviews=true", "host": ["{{base_url}}"], "path": ["api", "v1", "comparison", "matrix"], "query": [{"key": "include_attributes", "value": "true", "description": "Include product attributes"}, {"key": "include_reviews", "value": "true", "description": "Include review data"}]}, "description": "Get detailed comparison matrix"}, "response": []}]}, {"name": "🔧 Admin Operations", "description": "Administrative functions - requires admin authentication", "item": [{"name": "👥 User Management", "description": "Admin user management operations", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/users?page={{page}}&limit={{limit}}&role=all&status=all", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Results per page"}, {"key": "role", "value": "all", "description": "User role filter"}, {"key": "status", "value": "all", "description": "User status filter"}]}, "description": "Get all users with filtering options (Admin only)"}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "{{user_id}}"]}, "description": "Get specific user details (Admin only)"}, "response": []}, {"name": "Update User Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"is_active\": false,\n  \"reason\": \"Account suspended for policy violation\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/users/{{user_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users", "{{user_id}}", "status"]}, "description": "Update user account status (Admin only)"}, "response": []}]}, {"name": "📦 Product Management", "description": "Admin product management operations", "item": [{"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Laptop\",\n  \"description\": \"High-performance laptop for professionals\",\n  \"price\": 1299.99,\n  \"comparison_price\": 1499.99,\n  \"cost\": 999.99,\n  \"sku\": \"LAPTOP-001\",\n  \"category_id\": \"{{category_id}}\",\n  \"brand_id\": \"{{brand_id}}\",\n  \"weight\": 2.5,\n  \"dimensions\": {\n    \"length\": 35.5,\n    \"width\": 24.2,\n    \"height\": 1.9\n  },\n  \"attributes\": {\n    \"processor\": \"Intel i7\",\n    \"ram\": \"16GB\",\n    \"storage\": \"512GB SSD\",\n    \"screen_size\": \"15.6 inch\"\n  },\n  \"images\": [\n    \"https://example.com/laptop1.jpg\",\n    \"https://example.com/laptop2.jpg\"\n  ],\n  \"is_active\": true,\n  \"is_featured\": false,\n  \"meta_title\": \"Premium Laptop - High Performance\",\n  \"meta_description\": \"Professional laptop with Intel i7 processor\",\n  \"tags\": [\"laptop\", \"professional\", \"intel\", \"ssd\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products"]}, "description": "Create new product (Admin only)"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Product created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('name');", "    pm.expect(responseJson.data).to.have.property('sku');", "    pm.environment.set('product_id', responseJson.data.id);", "    pm.environment.set('product_sku', responseJson.data.sku);", "});"], "type": "text/javascript"}}]}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Premium Laptop - Updated\",\n  \"price\": 1199.99,\n  \"comparison_price\": 1399.99,\n  \"is_featured\": true,\n  \"attributes\": {\n    \"processor\": \"Intel i7-12th Gen\",\n    \"ram\": \"32GB\",\n    \"storage\": \"1TB SSD\",\n    \"screen_size\": \"15.6 inch\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{product_id}}"]}, "description": "Update existing product (Admin only)"}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/v1/admin/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products", "{{product_id}}"]}, "description": "Delete product (Admin only)"}, "response": []}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Enhanced Global Pre-request Script", "// Set common variables and handle authentication", "", "// Set common timestamp and random variables", "pm.environment.set('timestamp', new Date().toISOString());", "pm.environment.set('random_email', 'user' + Math.floor(Math.random() * 10000) + '@example.com');", "pm.environment.set('random_string', Math.random().toString(36).substring(7));", "", "// Set search and pagination defaults if not exists", "if (!pm.environment.get('search_query')) {", "    pm.environment.set('search_query', 'laptop');", "}", "if (!pm.environment.get('partial_query')) {", "    pm.environment.set('partial_query', 'lap');", "}", "if (!pm.environment.get('page')) {", "    pm.environment.set('page', '1');", "}", "if (!pm.environment.get('limit')) {", "    pm.environment.set('limit', '20');", "}", "if (!pm.environment.get('sort_by')) {", "    pm.environment.set('sort_by', 'relevance');", "}", "", "// Set price filters", "if (!pm.environment.get('min_price')) {", "    pm.environment.set('min_price', '100');", "}", "if (!pm.environment.get('max_price')) {", "    pm.environment.set('max_price', '2000');", "}", "", "// Set search filters as JSON string", "if (!pm.environment.get('search_filters')) {", "    pm.environment.set('search_filters', JSON.stringify({", "        brand: ['apple', 'dell'],", "        rating: [4, 5],", "        features: ['ssd', 'touchscreen']", "    }));", "}", "", "// Set selected suggestion for tracking", "if (!pm.environment.get('selected_suggestion')) {", "    pm.environment.set('selected_suggestion', 'laptop gaming');", "}", "", "// Set warehouse defaults", "if (!pm.environment.get('warehouse_id')) {", "    pm.environment.set('warehouse_id', 'warehouse_main');", "}", "if (!pm.environment.get('target_warehouse_id')) {", "    pm.environment.set('target_warehouse_id', 'warehouse_secondary');", "}", "", "// Get authentication tokens and session", "const url = pm.request.url.toString();", "const token = pm.environment.get('jwt_token') || pm.globals.get('jwt_token');", "const adminToken = pm.environment.get('admin_token') || pm.globals.get('admin_token');", "const sessionId = pm.environment.get('session_id') || pm.globals.get('session_id');", "", "// Remove existing auth headers to avoid duplicates", "pm.request.headers.remove('Authorization');", "pm.request.headers.remove('X-Session-ID');", "", "// Add appropriate authentication based on endpoint", "if (url.includes('/admin/') && adminToken) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + adminToken", "    });", "} else if (token && !url.includes('/auth/login') && !url.includes('/auth/register')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + token", "    });", "}", "", "// Add session ID for guest operations", "if ((url.includes('/cart') || url.includes('/comparison')) && sessionId && !token) {", "    pm.request.headers.add({", "        key: 'X-Session-ID',", "        value: sessionId", "    });", "}", "", "// Generate session ID if not exists", "if (!sessionId) {", "    const newSessionId = 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);", "    pm.environment.set('session_id', newSessionId);", "    if (!token) {", "        pm.request.headers.add({", "            key: 'X-Session-ID',", "            value: newSessionId", "        });", "    }", "}", "", "// Set Content-Type for JSON requests", "if (['POST', 'PUT', 'PATCH'].includes(pm.request.method) && pm.request.body) {", "    pm.request.headers.upsert({", "        key: 'Content-Type',", "        value: 'application/json'", "    });", "}", "", "// Set API version header", "pm.request.headers.upsert({", "    key: 'Accept',", "    value: 'application/vnd.api+json;version=1'", "});", "", "// Add request ID for tracking", "pm.request.headers.upsert({", "    key: 'X-Request-ID',", "    value: 'req_' + Math.random().toString(36).substring(2, 15)", "});", "", "// Log request details for debugging", "console.log('Request URL:', pm.request.url.toString());", "console.log('Request Method:', pm.request.method);", "console.log('<PERSON> <PERSON><PERSON>:', !!token);", "console.log('Has <PERSON><PERSON>:', !!adminToken);", "console.log('Has Session ID:', !!sessionId);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Enhanced Global Test Script", "// Comprehensive response validation and data extraction", "", "// Log response details for debugging", "console.log('Request:', pm.info.requestName);", "console.log('Response Status:', pm.response.code);", "console.log('Response Time:', pm.response.responseTime + 'ms');", "", "// Common response validation tests", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid status code', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 202, 204, 400, 401, 403, 404, 422, 500]);", "});", "", "// Handle successful responses", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    pm.test('Successful response structure', function () {", "        if (pm.response.code !== 204) { // No content responses don't have JSON", "            pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "            ", "            const responseJson = pm.response.json();", "            pm.expect(responseJson).to.be.an('object');", "            ", "            // Check for standard success response structure", "            if (responseJson.data !== undefined) {", "                pm.expect(responseJson).to.have.property('data');", "            }", "            if (responseJson.message !== undefined) {", "                pm.expect(responseJson).to.have.property('message');", "                pm.expect(responseJson.message).to.be.a('string');", "            }", "        }", "    });", "    ", "    // Extract and store important data", "    try {", "        if (pm.response.code !== 204) {", "            const responseJson = pm.response.json();", "            ", "            // Store authentication tokens - Handle multiple formats", "            // Format 1: New format with tokens object", "            if (responseJson.data && responseJson.data.tokens) {", "                if (responseJson.data.tokens.access_token) {", "                    pm.environment.set('jwt_token', responseJson.data.tokens.access_token);", "                    console.log('✅ JWT token stored (new format)');", "                }", "                if (responseJson.data.tokens.refresh_token) {", "                    pm.environment.set('refresh_token', responseJson.data.tokens.refresh_token);", "                    console.log('✅ Refresh token stored (new format)');", "                }", "            }", "            // Format 2: Backend format with direct token fields", "            else if (responseJson.data && responseJson.data.token) {", "                pm.environment.set('jwt_token', responseJson.data.token);", "                console.log('✅ JWT token stored (backend format)');", "                ", "                if (responseJson.data.refresh_token) {", "                    pm.environment.set('refresh_token', responseJson.data.refresh_token);", "                    console.log('✅ Refresh token stored (backend format)');", "                }", "            }", "            // Format 3: Legacy format for backward compatibility", "            else if (responseJson.token) {", "                pm.environment.set('jwt_token', responseJson.token);", "                console.log('✅ JWT token stored (legacy format)');", "            }", "            ", "            // Store user information", "            if (responseJson.data && responseJson.data.user) {", "                if (responseJson.data.user.id) {", "                    pm.environment.set('user_id', responseJson.data.user.id);", "                    console.log('✅ User ID stored:', responseJson.data.user.id);", "                }", "                if (responseJson.data.user.role === 'admin') {", "                    pm.environment.set('admin_token', pm.environment.get('jwt_token'));", "                    console.log('✅ Admin token stored');", "                }", "            }", "            ", "            // Store session information", "            if (responseJson.data && responseJson.data.session && responseJson.data.session.id) {", "                pm.environment.set('session_id', responseJson.data.session.id);", "                console.log('✅ Session ID stored');", "            }", "            ", "            // Store created resource IDs based on request name", "            if (responseJson.data && responseJson.data.id) {", "                const requestName = pm.info.requestName.toLowerCase();", "                const resourceId = responseJson.data.id;", "                ", "                if (requestName.includes('product')) {", "                    pm.environment.set('product_id', resourceId);", "                    pm.environment.set('last_product_id', resourceId);", "                } else if (requestName.includes('category')) {", "                    pm.environment.set('category_id', resourceId);", "                    pm.environment.set('last_category_id', resourceId);", "                } else if (requestName.includes('order')) {", "                    pm.environment.set('order_id', resourceId);", "                } else if (requestName.includes('address')) {", "                    pm.environment.set('address_id', resourceId);", "                } else if (requestName.includes('payment')) {", "                    pm.environment.set('payment_method_id', resourceId);", "                } else if (requestName.includes('comparison')) {", "                    pm.environment.set('comparison_id', resourceId);", "                } else if (requestName.includes('notification')) {", "                    pm.environment.set('notification_id', resourceId);", "                }", "                ", "                console.log('✅ Resource ID stored:', resourceId, 'for', requestName);", "            }", "            ", "            // Store pagination info for list endpoints", "            if (responseJson.data && responseJson.data.pagination) {", "                const pagination = responseJson.data.pagination;", "                pm.environment.set('current_page', pagination.page.toString());", "                pm.environment.set('total_pages', pagination.total_pages.toString());", "                pm.environment.set('total_items', pagination.total.toString());", "            }", "        }", "    } catch (e) {", "        console.log('Error parsing response JSON:', e.message);", "    }", "}", "", "// Handle error responses", "if (pm.response.code >= 400) {", "    pm.test('Error response structure', function () {", "        if (pm.response.headers.get('Content-Type') && pm.response.headers.get('Content-Type').includes('application/json')) {", "            const responseJson = pm.response.json();", "            pm.expect(responseJson).to.be.an('object');", "            pm.expect(responseJson).to.have.property('error');", "            pm.expect(responseJson.error).to.be.a('string');", "            ", "            // Log error details", "            console.log('❌ Error:', responseJson.error);", "            if (responseJson.details) {", "                console.log('❌ Error Details:', responseJson.details);", "            }", "        }", "    });", "}", "", "// Validate pagination structure for list endpoints", "if (pm.response.code === 200) {", "    try {", "        const responseJson = pm.response.json();", "        if (responseJson.data && responseJson.data.pagination) {", "            pm.test('Pagination structure is valid', function () {", "                const pagination = responseJson.data.pagination;", "                pm.expect(pagination).to.have.property('page');", "                pm.expect(pagination).to.have.property('limit');", "                pm.expect(pagination).to.have.property('total');", "                pm.expect(pagination).to.have.property('total_pages');", "                pm.expect(pagination).to.have.property('has_next');", "                pm.expect(pagination).to.have.property('has_prev');", "                ", "                // Validate pagination values", "                pm.expect(pagination.page).to.be.a('number');", "                pm.expect(pagination.limit).to.be.a('number');", "                pm.expect(pagination.total).to.be.a('number');", "                pm.expect(pagination.total_pages).to.be.a('number');", "                pm.expect(pagination.has_next).to.be.a('boolean');", "                pm.expect(pagination.has_prev).to.be.a('boolean');", "            });", "        }", "    } catch (e) {", "        // Pagination validation is optional", "    }", "}", "", "// Security headers validation", "pm.test('Security headers are present', function () {", "    // These tests are informational and won't fail the request", "    const headers = pm.response.headers;", "    ", "    if (headers.get('X-Request-ID')) {", "        console.log('🔍 Request ID:', headers.get('X-Request-ID'));", "    }", "    ", "    if (headers.get('X-RateLimit-Remaining')) {", "        console.log('⏱️ Rate Limit Remaining:', headers.get('X-RateLimit-Remaining'));", "    }", "});"]}}]}