# 📚 API Documentation & Examples

## 🎯 Overview
This document provides comprehensive documentation and examples for all API endpoints in the Ecommerce platform.

## 🔐 Authentication Endpoints

### POST /api/v1/auth/register
**Description**: Register a new user account

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "phone": "+**********"
}
```

**Success Response (201)**:
```json
{
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "phone": "+**********",
      "is_active": false,
      "email_verified": false,
      "created_at": "2024-01-01T12:00:00Z"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_at": **********
    }
  }
}
```

**Error Response (400)**:
```json
{
  "error": "Validation failed",
  "details": "Email already exists"
}
```

### POST /api/v1/auth/login
**Description**: Authenticate user and receive JWT tokens

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "ip_address": "127.0.0.1",
  "user_agent": "PostmanRuntime/7.32.3",
  "device_info": "Postman API Client"
}
```

**Success Response (200)**:
```json
{
  "message": "Login successful",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "role": "customer",
      "is_active": true,
      "email_verified": true
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_at": **********
    },
    "session": {
      "id": "session_123",
      "expires_at": "2024-01-02T12:00:00Z"
    }
  }
}
```

## 🔍 Search Endpoints

### GET /api/v1/search
**Description**: Perform full text search across products with filters

**Query Parameters**:
- `q` (string, required): Search query
- `category` (uuid, optional): Category filter
- `min_price` (float, optional): Minimum price filter
- `max_price` (float, optional): Maximum price filter
- `page` (int, optional): Page number (default: 1)
- `limit` (int, optional): Results per page (default: 20, max: 100)
- `sort` (string, optional): Sort by (relevance, price_asc, price_desc, newest, rating)

**Example Request**:
```
GET /api/v1/search?q=laptop&category=electronics&min_price=500&max_price=2000&page=1&limit=20&sort=price_asc
```

**Success Response (200)**:
```json
{
  "message": "Search completed successfully",
  "data": {
    "results": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "MacBook Pro 16-inch",
        "description": "Powerful laptop for professionals",
        "price": 1999.99,
        "comparison_price": 2299.99,
        "currency": "USD",
        "images": [
          {
            "url": "https://example.com/macbook-pro.jpg",
            "alt_text": "MacBook Pro 16-inch"
          }
        ],
        "category": {
          "id": "cat_electronics",
          "name": "Electronics",
          "slug": "electronics"
        },
        "brand": {
          "id": "brand_apple",
          "name": "Apple",
          "slug": "apple"
        },
        "rating": {
          "average": 4.8,
          "count": 1250
        },
        "stock": {
          "quantity": 15,
          "status": "in_stock"
        },
        "relevance_score": 0.95
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 156,
      "total_pages": 8,
      "has_next": true,
      "has_prev": false
    },
    "facets": {
      "categories": [
        {
          "id": "electronics",
          "name": "Electronics",
          "count": 89
        }
      ],
      "brands": [
        {
          "id": "apple",
          "name": "Apple",
          "count": 23
        }
      ],
      "price_ranges": [
        {
          "min": 500,
          "max": 1000,
          "count": 45
        }
      ]
    },
    "search_metadata": {
      "query": "laptop",
      "total_time_ms": 45,
      "suggestions": ["laptop gaming", "laptop apple", "laptop dell"]
    }
  }
}
```

### GET /api/v1/search/autocomplete
**Description**: Get autocomplete suggestions for search queries

**Query Parameters**:
- `q` (string, required): Partial search query
- `limit` (int, optional): Maximum suggestions (default: 10, max: 20)
- `include_products` (bool, optional): Include product suggestions
- `include_categories` (bool, optional): Include category suggestions

**Success Response (200)**:
```json
{
  "message": "Autocomplete suggestions retrieved",
  "data": {
    "suggestions": [
      {
        "text": "laptop gaming",
        "type": "query",
        "count": 156,
        "highlighted": "<strong>lap</strong>top gaming"
      },
      {
        "text": "laptop apple",
        "type": "query", 
        "count": 89,
        "highlighted": "<strong>lap</strong>top apple"
      }
    ],
    "products": [
      {
        "id": "prod_123",
        "name": "MacBook Pro",
        "price": 1999.99,
        "image": "https://example.com/macbook.jpg",
        "highlighted": "Mac<strong>b</strong>ook Pro"
      }
    ],
    "categories": [
      {
        "id": "cat_laptops",
        "name": "Laptops",
        "count": 234,
        "highlighted": "<strong>Lap</strong>tops"
      }
    ]
  }
}
```

## 🛍️ Shopping Cart Endpoints

### GET /api/v1/cart
**Description**: Get current user's shopping cart

**Headers**:
- `Authorization: Bearer {jwt_token}` (for authenticated users)
- `X-Session-ID: {session_id}` (for guest users)

**Success Response (200)**:
```json
{
  "message": "Cart retrieved successfully",
  "data": {
    "id": "cart_550e8400",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "session_id": "session_123",
    "items": [
      {
        "id": "item_001",
        "product_id": "550e8400-e29b-41d4-a716-************",
        "product": {
          "id": "550e8400-e29b-41d4-a716-************",
          "name": "MacBook Pro 16-inch",
          "price": 1999.99,
          "image": "https://example.com/macbook-pro.jpg",
          "stock": 15
        },
        "quantity": 2,
        "unit_price": 1999.99,
        "total_price": 3999.98,
        "added_at": "2024-01-01T10:30:00Z"
      }
    ],
    "totals": {
      "subtotal": 3999.98,
      "tax": 319.99,
      "shipping": 0.00,
      "discount": 0.00,
      "total": 4319.97
    },
    "item_count": 2,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:30:00Z"
  }
}
```

### POST /api/v1/cart/items
**Description**: Add item to shopping cart

**Request Body**:
```json
{
  "product_id": "550e8400-e29b-41d4-a716-************",
  "quantity": 2,
  "variant_id": "variant_123",
  "options": {
    "color": "Space Gray",
    "storage": "512GB"
  }
}
```

**Success Response (201)**:
```json
{
  "message": "Item added to cart successfully",
  "data": {
    "item": {
      "id": "item_002",
      "product_id": "550e8400-e29b-41d4-a716-************",
      "quantity": 2,
      "unit_price": 1999.99,
      "total_price": 3999.98
    },
    "cart_totals": {
      "subtotal": 7999.96,
      "item_count": 4,
      "total": 8639.95
    }
  }
}
```

## 📦 Product Management Endpoints

### GET /api/v1/products/{id}
**Description**: Get detailed product information

**Path Parameters**:
- `id` (uuid, required): Product ID

**Success Response (200)**:
```json
{
  "message": "Product retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "MacBook Pro 16-inch",
    "description": "The most powerful MacBook Pro ever...",
    "short_description": "Powerful laptop for professionals",
    "sku": "MBP-16-2024",
    "slug": "macbook-pro-16-inch",
    "price": 1999.99,
    "comparison_price": 2299.99,
    "cost": 1500.00,
    "currency": "USD",
    "status": "active",
    "visibility": "visible",
    "featured": true,
    "product_type": "simple",
    "is_digital": false,
    "weight": 2.1,
    "dimensions": {
      "length": 35.57,
      "width": 24.59,
      "height": 1.68,
      "unit": "cm"
    },
    "category": {
      "id": "cat_laptops",
      "name": "Laptops",
      "slug": "laptops"
    },
    "brand": {
      "id": "brand_apple",
      "name": "Apple",
      "slug": "apple"
    },
    "images": [
      {
        "id": "img_001",
        "url": "https://example.com/macbook-pro-1.jpg",
        "alt_text": "MacBook Pro front view",
        "position": 1,
        "is_primary": true
      }
    ],
    "attributes": [
      {
        "name": "Color",
        "value": "Space Gray",
        "position": 1
      },
      {
        "name": "Storage",
        "value": "512GB SSD",
        "position": 2
      }
    ],
    "stock": {
      "quantity": 15,
      "status": "in_stock",
      "low_stock_threshold": 5,
      "track_quantity": true,
      "allow_backorder": false
    },
    "seo": {
      "meta_title": "MacBook Pro 16-inch - Apple",
      "meta_description": "Buy the new MacBook Pro 16-inch...",
      "keywords": "macbook, pro, apple, laptop"
    },
    "rating": {
      "average": 4.8,
      "count": 1250,
      "distribution": {
        "5": 950,
        "4": 200,
        "3": 75,
        "2": 15,
        "1": 10
      }
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

## 🎯 Common Response Patterns

### Success Response Structure
```json
{
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response Structure
```json
{
  "error": "Error type or message",
  "details": "Detailed error description"
}
```

### Pagination Structure
```json
{
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 156,
    "total_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

## 🔧 Common Headers

### Authentication
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Content Type
```
Content-Type: application/json
```

### Session (for guest operations)
```
X-Session-ID: session_123456789
```

### API Version
```
Accept: application/vnd.api+json;version=1
```
