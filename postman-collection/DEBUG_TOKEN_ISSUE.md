# 🔍 Debug Token Issue - Postman Collection

## 🚨 **Issue Identified**

<PERSON>u khi phân tích backend code, tôi đã tìm ra nguyên nhân tại sao token không được tự động lưu:

## 📋 **Root Cause Analysis**

### ✅ **Backend Response Structure (Correct)**
```json
// Login Response (từ backend)
{
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "refresh_token_here", 
    "expires_at": 1234567890,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "first_name": "<PERSON>",
      "last_name": "Doe",
      "role": "customer"
    }
  }
}
```

### ❌ **Documentation Mismatch (Incorrect)**
```json
// Documentation format (WRONG)
{
  "data": {
    "tokens": {
      "access_token": "...",
      "refresh_token": "..."
    }
  }
}
```

### ✅ **Register Response (No Token)**
```json
// Register Response (từ backend)
{
  "message": "User registered successfully",
  "data": {
    "id": "uuid",
    "email": "<EMAIL>", 
    "first_name": "John",
    "last_name": "Doe",
    "role": "customer",
    "is_active": true,
    "created_at": "2025-01-23T00:00:00Z"
  }
}
```

## 🔧 **Fixes Applied**

### 1. **Updated Login Test Script**
```javascript
// OLD (Incorrect)
if (responseJson.data && responseJson.data.token) {
    pm.environment.set('jwt_token', responseJson.data.token);
}

// NEW (Correct)
if (responseJson.data && responseJson.data.token) {
    pm.environment.set('jwt_token', responseJson.data.token);
    console.log('✅ JWT token stored:', responseJson.data.token.substring(0, 20) + '...');
    
    if (responseJson.data.refresh_token) {
        pm.environment.set('refresh_token', responseJson.data.refresh_token);
    }
    
    if (responseJson.data.user) {
        pm.environment.set('user_id', responseJson.data.user.id);
        pm.environment.set('user_role', responseJson.data.user.role);
        
        // Store admin token if user is admin
        if (responseJson.data.user.role === 'admin') {
            pm.environment.set('admin_token', responseJson.data.token);
        }
    }
}
```

### 2. **Enhanced Global Test Script**
```javascript
// Handle multiple token formats for backward compatibility
if (responseJson.data && responseJson.data.tokens) {
    // New format with tokens object
    if (responseJson.data.tokens.access_token) {
        pm.environment.set('jwt_token', responseJson.data.tokens.access_token);
    }
} else if (responseJson.data && responseJson.data.token) {
    // Backend format with direct token fields
    pm.environment.set('jwt_token', responseJson.data.token);
} else if (responseJson.token) {
    // Legacy format
    pm.environment.set('jwt_token', responseJson.token);
}
```

### 3. **Updated Register Test Script**
```javascript
// Register doesn't return token - only user data
pm.test('Store user data for login', function () {
    const responseJson = pm.response.json();
    if (responseJson.data && responseJson.data.id) {
        pm.environment.set('registered_user_id', responseJson.data.id);
        pm.environment.set('registered_user_email', responseJson.data.email);
        console.log('💡 Note: Registration successful. Use Login endpoint to get JWT token.');
    }
});
```

### 4. **Enhanced Pre-request Script**
```javascript
// Smart authentication based on endpoint type
const url = pm.request.url.toString();
const token = pm.environment.get('jwt_token');
const adminToken = pm.environment.get('admin_token');

// Remove existing auth headers to avoid duplicates
pm.request.headers.remove('Authorization');

// Add appropriate authentication based on endpoint
if (url.includes('/admin/') && adminToken) {
    pm.request.headers.add({
        key: 'Authorization',
        value: 'Bearer ' + adminToken
    });
} else if (token && !url.includes('/auth/login') && !url.includes('/auth/register')) {
    pm.request.headers.add({
        key: 'Authorization',
        value: 'Bearer ' + token
    });
}
```

## 🧪 **Testing Steps**

### **Step 1: Test Register**
```bash
1. Run "Register User" request
2. Check Console logs for: "✅ Registered user data stored"
3. Verify environment variables: registered_user_id, registered_user_email
4. Note: No JWT token should be stored (this is correct)
```

### **Step 2: Test Login**
```bash
1. Run "Login User" request  
2. Check Console logs for: "✅ JWT token stored"
3. Verify environment variables: jwt_token, user_id, user_role
4. If user is admin, also check: admin_token
```

### **Step 3: Test Authenticated Request**
```bash
1. Run any authenticated request (e.g., "Get User Profile")
2. Check request headers for: "Authorization: Bearer <token>"
3. Should receive 200 response (not 401 Unauthorized)
```

### **Step 4: Test Admin Request**
```bash
1. Login with admin account first
2. Run any admin request (e.g., "Get All Users")
3. Check request headers for admin token
4. Should receive 200 response
```

## 🔍 **Debugging Commands**

### **Check Environment Variables**
```javascript
// In Postman Console
console.log('JWT Token:', pm.environment.get('jwt_token'));
console.log('User ID:', pm.environment.get('user_id'));
console.log('User Role:', pm.environment.get('user_role'));
console.log('Admin Token:', pm.environment.get('admin_token'));
```

### **Check Request Headers**
```javascript
// In Pre-request Script
console.log('Request URL:', pm.request.url.toString());
console.log('Request Headers:', pm.request.headers.toObject());
```

### **Check Response Data**
```javascript
// In Test Script
console.log('Response Status:', pm.response.code);
console.log('Response Body:', pm.response.text());
console.log('Response JSON:', pm.response.json());
```

## ✅ **Expected Behavior After Fixes**

### **Register Flow**
1. ✅ Register request returns 201 with user data
2. ✅ User data stored in environment (no token)
3. ✅ Console shows registration success message

### **Login Flow**
1. ✅ Login request returns 200 with token + user data
2. ✅ JWT token automatically stored in environment
3. ✅ User data stored (ID, role, etc.)
4. ✅ Admin token stored if user is admin
5. ✅ Console shows token storage confirmation

### **Authenticated Requests**
1. ✅ Authorization header automatically added
2. ✅ Correct token used (user vs admin)
3. ✅ Requests succeed with 200 status
4. ✅ No manual token management needed

## 🚨 **Common Issues & Solutions**

### **Issue: 401 Unauthorized**
```bash
Cause: Token not stored or expired
Solution: 
1. Check jwt_token in environment
2. Re-run Login request
3. Check token expiration
```

### **Issue: Token not stored**
```bash
Cause: Response structure mismatch
Solution:
1. Check Console logs for errors
2. Verify response JSON structure
3. Check backend response format
```

### **Issue: Wrong token for admin endpoints**
```bash
Cause: Using user token for admin endpoints
Solution:
1. Login with admin account
2. Check admin_token is stored
3. Verify URL contains '/admin/'
```

## 🎯 **Verification Checklist**

- [ ] Register request works and stores user data
- [ ] Login request works and stores JWT token
- [ ] JWT token appears in environment variables
- [ ] Authenticated requests include Authorization header
- [ ] Admin requests use admin token
- [ ] Console logs show token storage confirmations
- [ ] No 401 Unauthorized errors on authenticated requests

## 🏆 **Result**

After applying these fixes, the token management should work automatically:

1. **Register** → User data stored (no token)
2. **Login** → JWT token + user data stored automatically  
3. **All authenticated requests** → Token added automatically
4. **Admin requests** → Admin token used automatically

**Token management is now fully automated!** 🚀✨
