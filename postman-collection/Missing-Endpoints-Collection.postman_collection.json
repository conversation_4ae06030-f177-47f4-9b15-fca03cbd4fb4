{"info": {"_postman_id": "missing-endpoints-collection", "name": "Missing Endpoints - Ecommerce API", "description": "Collection containing all missing API endpoints identified in the analysis", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔍 Search & Autocomplete", "item": [{"name": "Full Text Search", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/search?q={{search_query}}&category={{category_id}}&min_price={{min_price}}&max_price={{max_price}}&page={{page}}&limit={{limit}}", "host": ["{{base_url}}"], "path": ["api", "v1", "search"], "query": [{"key": "q", "value": "{{search_query}}", "description": "Search query string"}, {"key": "category", "value": "{{category_id}}", "description": "Category filter"}, {"key": "min_price", "value": "{{min_price}}", "description": "Minimum price filter"}, {"key": "max_price", "value": "{{max_price}}", "description": "Maximum price filter"}, {"key": "page", "value": "{{page}}", "description": "Page number"}, {"key": "limit", "value": "{{limit}}", "description": "Results per page"}]}, "description": "Perform full text search across products with filters"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has search results', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('results');", "    pm.expect(responseJson.data.results).to.be.an('array');", "});", "", "pm.test('Pagination is present', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('pagination');", "    pm.expect(responseJson.data.pagination).to.have.property('total');", "});"], "type": "text/javascript"}}]}, {"name": "Enhanced Search", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/search/enhanced?q={{search_query}}&filters={{search_filters}}&sort={{sort_by}}&facets=true", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "enhanced"], "query": [{"key": "q", "value": "{{search_query}}", "description": "Search query"}, {"key": "filters", "value": "{{search_filters}}", "description": "JSON encoded filters"}, {"key": "sort", "value": "{{sort_by}}", "description": "Sort criteria"}, {"key": "facets", "value": "true", "description": "Include facets in response"}]}, "description": "Enhanced search with facets and advanced filtering"}, "response": []}, {"name": "Search Facets", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/search/facets?q={{search_query}}&category={{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "facets"], "query": [{"key": "q", "value": "{{search_query}}", "description": "Search query"}, {"key": "category", "value": "{{category_id}}", "description": "Category filter"}]}, "description": "Get available search facets for filtering"}, "response": []}, {"name": "Basic Autocomplete", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/search/autocomplete?q={{partial_query}}&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "autocomplete"], "query": [{"key": "q", "value": "{{partial_query}}", "description": "Partial search query"}, {"key": "limit", "value": "10", "description": "Maximum suggestions"}]}, "description": "Get basic autocomplete suggestions"}, "response": []}, {"name": "Enhanced Autocomplete", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/search/autocomplete/enhanced?q={{partial_query}}&include_products=true&include_categories=true&limit=15", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "autocomplete", "enhanced"], "query": [{"key": "q", "value": "{{partial_query}}", "description": "Partial search query"}, {"key": "include_products", "value": "true", "description": "Include product suggestions"}, {"key": "include_categories", "value": "true", "description": "Include category suggestions"}, {"key": "limit", "value": "15", "description": "Maximum suggestions"}]}, "description": "Get enhanced autocomplete with products and categories"}, "response": []}, {"name": "Smart Autocomplete", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/search/autocomplete/smart?q={{partial_query}}&personalized=true&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "autocomplete", "smart"], "query": [{"key": "q", "value": "{{partial_query}}", "description": "Partial search query"}, {"key": "personalized", "value": "true", "description": "Use personalization"}, {"key": "limit", "value": "20", "description": "Maximum suggestions"}]}, "description": "Get smart autocomplete with personalization"}, "response": []}, {"name": "Track Autocomplete Interaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"{{search_query}}\",\n  \"suggestion\": \"{{selected_suggestion}}\",\n  \"position\": 1,\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"timestamp\": \"{{$timestamp}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/search/autocomplete/track", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "autocomplete", "track"]}, "description": "Track autocomplete interaction for analytics"}, "response": []}, {"name": "Trending Searches", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/search/trending?period=24h&limit=10", "host": ["{{base_url}}"], "path": ["api", "v1", "search", "trending"], "query": [{"key": "period", "value": "24h", "description": "Time period (1h, 24h, 7d, 30d)"}, {"key": "limit", "value": "10", "description": "Maximum trending searches"}]}, "description": "Get trending search queries"}, "response": []}]}, {"name": "🤖 Recommendations", "item": [{"name": "General Recommendations", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/recommendations?type=general&limit=10&category={{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations"], "query": [{"key": "type", "value": "general", "description": "Recommendation type"}, {"key": "limit", "value": "10", "description": "Number of recommendations"}, {"key": "category", "value": "{{category_id}}", "description": "Category filter"}]}, "description": "Get general product recommendations"}, "response": []}, {"name": "Trending Products", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/recommendations/trending?period=7d&limit=20&category={{category_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "trending"], "query": [{"key": "period", "value": "7d", "description": "Time period (1d, 7d, 30d)"}, {"key": "limit", "value": "20", "description": "Number of trending products"}, {"key": "category", "value": "{{category_id}}", "description": "Category filter"}]}, "description": "Get trending products"}, "response": []}, {"name": "Track Recommendation Interaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"{{product_id}}\",\n  \"recommendation_type\": \"trending\",\n  \"position\": 1,\n  \"action\": \"click\",\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"{{user_id}}\",\n  \"context\": {\n    \"page\": \"homepage\",\n    \"section\": \"trending_products\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/recommendations/track", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "track"]}, "description": "Track recommendation interaction for analytics"}, "response": []}, {"name": "Personalized Recommendations", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/recommendations/personalized?limit=15&exclude_purchased=true&include_similar=true", "host": ["{{base_url}}"], "path": ["api", "v1", "recommendations", "personalized"], "query": [{"key": "limit", "value": "15", "description": "Number of recommendations"}, {"key": "exclude_purchased", "value": "true", "description": "Exclude already purchased items"}, {"key": "include_similar", "value": "true", "description": "Include similar products"}]}, "description": "Get personalized recommendations for authenticated user"}, "response": []}]}, {"name": "📍 Address Management", "item": [{"name": "Get User Addresses", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/v1/addresses", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses"]}, "description": "Get all addresses for authenticated user"}, "response": []}, {"name": "Create Address", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"shipping\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"company\": \"Tech Corp\",\n  \"address_line_1\": \"123 Main Street\",\n  \"address_line_2\": \"Apt 4B\",\n  \"city\": \"New York\",\n  \"state\": \"NY\",\n  \"postal_code\": \"10001\",\n  \"country\": \"US\",\n  \"phone\": \"+1234567890\",\n  \"is_default\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/addresses", "host": ["{{base_url}}"], "path": ["api", "v1", "addresses"]}, "description": "Create a new address for user"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Address created successfully', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.environment.set('address_id', responseJson.data.id);", "});"], "type": "text/javascript"}}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set common variables", "pm.environment.set('search_query', 'laptop');", "pm.environment.set('partial_query', 'lap');", "pm.environment.set('category_id', '');", "pm.environment.set('min_price', '100');", "pm.environment.set('max_price', '2000');", "pm.environment.set('page', '1');", "pm.environment.set('limit', '20');", "pm.environment.set('sort_by', 'relevance');", "pm.environment.set('search_filters', JSON.stringify({brand: ['apple', 'dell']}));", "pm.environment.set('selected_suggestion', 'laptop gaming');", "", "// Add Authorization header if token exists", "const token = pm.environment.get('jwt_token');", "if (token && pm.request.method !== 'GET') {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + token", "    });", "}"]}}]}